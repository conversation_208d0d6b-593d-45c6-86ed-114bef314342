#!/usr/bin/env node

/**
 * Скрипт для исправления индексов MeiliSearch
 */

const { MeiliSearch } = require('meilisearch');

async function main() {
  try {
    // Получаем URL MeiliSearch из переменных окружения или используем значение по умолчанию
    const searchUrl = process.env.PUBLIC_SEARCH_URL || 'https://search.stom-line.ru';
    const searchApiKey = process.env.PUBLIC_SEARCH_API_KEY || 'Nc040stomline';
    
    console.log(`Подключение к MeiliSearch: ${searchUrl}`);
    
    // Создаем экземпляр MeiliSearch
    const searchClient = new MeiliSearch({
      host: searchUrl,
      apiKey: searchApiKey
    });
    
    // Удаляем проблемные индексы
    console.log('\n=== Удаление проблемных индексов ===');
    
    try {
      await searchClient.deleteIndex('services');
      console.log('Индекс "services" успешно удален');
    } catch (error) {
      console.error('Ошибка при удалении индекса "services":', error.message);
    }
    
    try {
      await searchClient.deleteIndex('prices');
      console.log('Индекс "prices" успешно удален');
    } catch (error) {
      console.error('Ошибка при удалении индекса "prices":', error.message);
    }
    
    try {
      await searchClient.deleteIndex('all');
      console.log('Индекс "all" успешно удален');
    } catch (error) {
      console.error('Ошибка при удалении индекса "all":', error.message);
    }
    
    // Создаем индексы заново с указанием первичного ключа
    console.log('\n=== Создание индексов с первичным ключом ===');
    
    try {
      await searchClient.createIndex('services', { primaryKey: 'id' });
      console.log('Индекс "services" успешно создан с первичным ключом "id"');
    } catch (error) {
      console.error('Ошибка при создании индекса "services":', error.message);
    }
    
    try {
      await searchClient.createIndex('prices', { primaryKey: 'id' });
      console.log('Индекс "prices" успешно создан с первичным ключом "id"');
    } catch (error) {
      console.error('Ошибка при создании индекса "prices":', error.message);
    }
    
    try {
      await searchClient.createIndex('all', { primaryKey: 'id' });
      console.log('Индекс "all" успешно создан с первичным ключом "id"');
    } catch (error) {
      console.error('Ошибка при создании индекса "all":', error.message);
    }
    
    console.log('\nИсправление индексов завершено. Теперь запустите скрипт синхронизации данных.');
  } catch (error) {
    console.error('Ошибка при выполнении скрипта:', error);
    process.exit(1);
  }
}

// Запускаем скрипт
main();
