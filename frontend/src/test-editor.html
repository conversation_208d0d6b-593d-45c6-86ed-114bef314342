<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест редактора</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .editor-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-content {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background-color: white;
            color: #1f2937;
            min-height: 200px;
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <h1>Тест цвета текста в редакторе</h1>
        <p>Этот файл поможет проверить, что цвет текста отображается правильно.</p>
        
        <div class="test-content">
            <h2>Заголовок 2</h2>
            <p>Это обычный абзац текста. Он должен быть темного цвета (#1f2937) на белом фоне.</p>
            <ul>
                <li>Первый элемент списка</li>
                <li>Второй элемент списка</li>
            </ul>
            <blockquote>
                Это цитата, которая должна быть видна и читаема.
            </blockquote>
        </div>
        
        <p><strong>Инструкции:</strong></p>
        <ol>
            <li>Откройте компонент TiptapEditorWrapper в вашем приложении</li>
            <li>Убедитесь, что весь текст темного цвета и хорошо читается</li>
            <li>Попробуйте ввести новый текст - он должен быть темным</li>
            <li>Если текст все еще белый, проверьте CSS файлы на конфликты</li>
        </ol>
    </div>
</body>
</html>
