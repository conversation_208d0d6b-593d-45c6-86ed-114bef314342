/// <reference types="astro/client" />

interface ImportMetaEnv {
  // Публичные переменные (доступны на клиенте)
  readonly PUBLIC_API_URL: string;

  // Приватные переменные (доступны только на сервере)
  readonly STRAPI_API_TOKEN: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Типы для Astro.locals
declare namespace App {
  interface Locals {
    isAuthenticated: boolean;
    user?: {
      id: string;
      email: string;
      token: string;
      type: 'admin' | 'user';
    };
  }
}
