"use client"
import type React from "react"
import { useEffect, useRef, useState } from "react"
import { motion, useTransform, useScroll, useSpring } from "framer-motion"
import { cn } from "@/lib/utils"

export const TracingBeam = ({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) => {
  const ref = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end end"],
  })

  const contentRef = useRef<HTMLDivElement>(null)
  const [svgHeight, setSvgHeight] = useState(0)

  useEffect(() => {
    const updateHeight = () => {
      if (contentRef.current) {
        setSvgHeight(contentRef.current.offsetHeight)
      }
    }

    // Initial height
    updateHeight()

    // Update height on resize
    window.addEventListener("resize", updateHeight)

    // Update height after a short delay to ensure all content is rendered
    const timeout = setTimeout(updateHeight, 1000)

    return () => {
      window.removeEventListener("resize", updateHeight)
      clearTimeout(timeout)
    }
  }, [])

  const y1 = useSpring(useTransform(scrollYProgress, [0, 0.8], [50, svgHeight]), {
    stiffness: 500,
    damping: 90,
  })
  const y2 = useSpring(useTransform(scrollYProgress, [0, 1], [50, svgHeight - 200]), {
    stiffness: 500,
    damping: 90,
  })

  return (
    <motion.div ref={ref} className={cn("relative mx-auto h-full w-full max-w-screen-xl", className)}>
      <div className="absolute top-3 -left-4 md:-left-20 h-full z-10">
        <motion.div
          transition={{
            duration: 0.2,
            delay: 0.5,
          }}
          initial={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}
          animate={{
            boxShadow: scrollYProgress.get() > 0 ? "none" : "rgba(0, 0, 0, 0.24) 0px 3px 8px",
          }}
          className="border-netural-200 ml-[27px] flex h-4 w-4 items-center justify-center rounded-full border shadow-sm"
        >
          <motion.div
            transition={{
              duration: 0.2,
              delay: 0.5,
            }}
            initial={{ backgroundColor: "#4D8C29", borderColor: "#85C028" }}
            animate={{
              backgroundColor: scrollYProgress.get() > 0 ? "white" : "#4D8C29",
              borderColor: scrollYProgress.get() > 0 ? "white" : "#85C028",
            }}
            className="h-2 w-2 rounded-full border border-neutral-300 bg-white"
          />
        </motion.div>
        <svg
          viewBox={`0 0 20 ${Math.max(svgHeight, 5000)}`}
          width="20"
          height={Math.max(svgHeight, 5000)}
          className="ml-4 block"
          aria-hidden="true"
          style={{ overflow: "visible" }}
        >
          <motion.path
            d={`M 1 0V -36 l 18 24 V ${svgHeight * 0.8} l -18 24V ${svgHeight}`}
            fill="none"
            stroke="#9091A0"
            strokeOpacity="0.16"
            strokeWidth="2"
            transition={{
              duration: 10,
            }}
          />
          <motion.path
            d={`M 1 0V -36 l 18 24 V ${svgHeight * 0.8} l -18 24V ${svgHeight}`}
            fill="none"
            stroke="url(#gradient)"
            strokeWidth="2"
            className="motion-reduce:hidden"
            transition={{
              duration: 10,
            }}
          />
          <defs>
            <motion.linearGradient
              id="gradient"
              gradientUnits="userSpaceOnUse"
              x1="0"
              x2="0"
              y1={y1} // set y1 for gradient
              y2={y2} // set y2 for gradient
            >
              <stop stopColor="#4D8C29" stopOpacity="0" />
              <stop stopColor="#4D8C29" />
              <stop offset="0.325" stopColor="#85C028" />
              <stop offset="1" stopColor="#85C028" stopOpacity="0" />
            </motion.linearGradient>
          </defs>
        </svg>
      </div>
      <div ref={contentRef}>{children}</div>
    </motion.div>
  )
}
