import React from 'react';
import type { HtmlBlocks } from '@/lib/api';

// Компонент для безопасного рендеринга HTML
interface HtmlContentProps {
  content: string;
  className?: string;
}

const HtmlContent: React.FC<HtmlContentProps> = ({ content, className = "" }) => {
  return (
    <div 
      className={className}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

// Пример компонента, использующего HTML блоки
interface HtmlContentExampleProps {
  htmlBlocks?: HtmlBlocks[];
}

export default function HtmlContentExample({ htmlBlocks = [] }: HtmlContentExampleProps) {
  // Функция для получения контента блока по ключу
  const getBlockContent = (key: string, fallback: string = '') => {
    const block = htmlBlocks.find(b => b.key === key);
    return block?.content || fallback;
  };

  // Функция для рендеринга контента блока (поддерживает HTML)
  const renderBlockContent = (key: string, fallback: string = '', className?: string) => {
    const block = htmlBlocks.find(b => b.key === key);
    const content = block?.content || fallback;
    
    // Если блок имеет тип 'html', рендерим как HTML
    if (block?.type === 'html') {
      return <HtmlContent content={content} className={className} />;
    }
    
    // Иначе рендерим как обычный текст
    return <span className={className}>{content}</span>;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">
        Примеры использования HTML в блоках контента
      </h1>

      {/* Пример 1: Обычный текст */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          Обычный текстовый блок
        </h2>
        <div className="text-gray-600">
          {renderBlockContent('example_text', 'Это обычный текст без HTML разметки.')}
        </div>
        <div className="mt-4 text-sm text-gray-500">
          Тип блока: text | Ключ: example_text
        </div>
      </div>

      {/* Пример 2: HTML блок с форматированием */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          HTML блок с форматированием
        </h2>
        <div className="text-gray-600 prose prose-sm max-w-none">
          {renderBlockContent(
            'example_html_formatted', 
            '<p>Это <strong>жирный текст</strong> и <em>курсивный текст</em>.</p>',
            'leading-relaxed'
          )}
        </div>
        <div className="mt-4 text-sm text-gray-500">
          Тип блока: html | Ключ: example_html_formatted
        </div>
      </div>

      {/* Пример 3: HTML блок со списком */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          HTML блок со списком
        </h2>
        <div className="text-gray-600">
          {renderBlockContent(
            'example_html_list',
            '<p>Наши услуги:</p><ul><li>Терапевтическая стоматология</li><li>Хирургическая стоматология</li><li>Имплантология</li></ul>',
            'prose prose-sm max-w-none'
          )}
        </div>
        <div className="mt-4 text-sm text-gray-500">
          Тип блока: html | Ключ: example_html_list
        </div>
      </div>

      {/* Пример 4: HTML блок с ссылкой */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          HTML блок с ссылкой
        </h2>
        <div className="text-gray-600">
          {renderBlockContent(
            'example_html_link',
            '<p>Запишитесь на <a href="/appointment" class="text-blue-600 hover:text-blue-800 underline">бесплатную консультацию</a> уже сегодня!</p>',
            'prose prose-sm max-w-none'
          )}
        </div>
        <div className="mt-4 text-sm text-gray-500">
          Тип блока: html | Ключ: example_html_link
        </div>
      </div>

      {/* Пример 5: HTML блок с цветным текстом */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          HTML блок с цветным текстом
        </h2>
        <div className="text-gray-600">
          {renderBlockContent(
            'example_html_colored',
            '<p>Мы работаем с <span style="color: #4E8C29; font-weight: bold;">2008 года</span> и принимаем <span style="color: #8BC34A; font-weight: bold;">более 2500 пациентов</span> в год.</p>',
            'prose prose-sm max-w-none'
          )}
        </div>
        <div className="mt-4 text-sm text-gray-500">
          Тип блока: html | Ключ: example_html_colored
        </div>
      </div>

      {/* Инструкции */}
      <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
        <h2 className="text-xl font-semibold mb-4 text-blue-900">
          Как использовать
        </h2>
        <div className="text-blue-800 space-y-2">
          <p>1. В PocketBase создайте блок с типом 'html'</p>
          <p>2. Добавьте HTML разметку в поле content</p>
          <p>3. Используйте renderBlockContent() вместо getBlockContent()</p>
          <p>4. HTML будет автоматически отрендерен для блоков типа 'html'</p>
        </div>
      </div>

      {/* Примеры HTML кода */}
      <div className="bg-gray-50 rounded-lg border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          Примеры HTML кода для блоков
        </h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Форматированный текст:</h3>
            <code className="block bg-white p-3 rounded border text-sm">
              {`<p>Это <strong>жирный</strong> и <em>курсивный</em> текст.</p>`}
            </code>
          </div>
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Список:</h3>
            <code className="block bg-white p-3 rounded border text-sm">
              {`<ul><li>Пункт 1</li><li>Пункт 2</li><li>Пункт 3</li></ul>`}
            </code>
          </div>
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Ссылка:</h3>
            <code className="block bg-white p-3 rounded border text-sm">
              {`<p>Текст с <a href="/page">ссылкой</a>.</p>`}
            </code>
          </div>
        </div>
      </div>
    </div>
  );
}
