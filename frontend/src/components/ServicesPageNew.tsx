import * as React from 'react';
import { motion } from 'framer-motion';
import {
  Star,
  Stethoscope,
  Search
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { EditButton } from '@/components/admin/EditButton';
import { type Service } from '@/lib/api';



interface ServicesPageNewProps {
  services: Service[];
  specialist?: string;
  isAuthenticated?: boolean;
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 60 },
  visible: { 
    opacity: 1, 
    y: 0, 
    transition: { 
      duration: 0.6, 
      ease: [0.25, 0.25, 0, 1] 
    } 
  }
};





export const ServicesPageNew: React.FC<ServicesPageNewProps> = ({
  services,
  specialist,
  isAuthenticated = false
}) => {
  const [specialistName, setSpecialistName] = React.useState<string>('');
  const [searchQuery, setSearchQuery] = React.useState<string>('');

  // Получаем имя специалиста, если есть ID
  React.useEffect(() => {
    const fetchSpecialistName = async () => {
      if (specialist) {
        try {
          const response = await fetch(`${import.meta.env.PUBLIC_API_URL}/api/collections/doctors/records/${specialist}`);
          if (response.ok) {
            const data = await response.json();
            setSpecialistName(`${data.surname} ${data.name} ${data.patronymic || ''}`.trim());
          }
        } catch (error) {
          console.error('Ошибка при получении имени специалиста:', error);
        }
      }
    };

    fetchSpecialistName();
  }, [specialist]);



  // Фильтрация услуг
  const filteredServices = React.useMemo(() => {
    let filtered = services;

    // Фильтр по поисковому запросу
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(query) ||
        service.short_description?.toLowerCase().includes(query) ||
        service.expand?.category?.name.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [services, searchQuery]);

  return (
    <div className="relative flex min-h-screen flex-col bg-gradient-to-br from-olive-50 via-white to-olive-50/50">
      {/* Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]" />
        <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]" />
        <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]" />
      </div>

      {/* Compact Hero Section */}
      <section className="relative z-10 py-3 sm:py-4 lg:py-5">
        <div className="container mx-auto px-4 md:px-6">
          <motion.div
            className="text-center"
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
          >
            <div className="inline-flex items-center rounded-full bg-gradient-to-r from-olive-100/80 to-olive-200/80 px-4 py-2 text-sm font-medium text-olive-700 backdrop-blur-sm shadow-lg border border-olive-200/50 mb-3">
              <Stethoscope className="mr-2 h-4 w-4" />
              {specialist && specialistName ? `Услуги специалиста` : 'Наши услуги'}
            </div>

            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent mb-3 leading-tight">
              {specialist && specialistName
                ? specialistName
                : 'Стоматологические услуги'
              }
            </h1>

            <p className="text-lg sm:text-xl lg:text-2xl text-olive-700 max-w-3xl mx-auto mb-4 leading-relaxed font-medium">
              {specialist && specialistName
                ? `Полный спектр услуг от ${specialistName}`
                : 'Современные технологии и индивидуальный подход'
              }
            </p>
          </motion.div>
        </div>
      </section>

      {/* Minimal Search Section */}
      <section className="relative z-10 py-3 lg:py-4">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-2xl mx-auto text-center">
            <div className="relative mb-3">
              <Search className="absolute left-4 top-1/2 h-6 w-6 -translate-y-1/2 text-olive-500" />
              <Input
                type="text"
                placeholder="Найти услугу..."
                className="pl-14 pr-4 bg-white border-olive-200 focus:border-olive-400 h-14 text-xl rounded-xl shadow-sm font-medium"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="relative z-10 py-4 lg:py-5">
        <div className="container mx-auto px-4 md:px-6">
          {filteredServices.length > 0 ? (
            <div className="space-y-3 lg:space-y-4">
              {filteredServices.map((service, index) => {
                const isEven = index % 2 === 0;
                const hasImage = service.image;

                return (
                  <motion.div
                    key={service.id}
                    variants={fadeInUp}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.1 }}
                    className="group cursor-pointer transition-all duration-300 hover:scale-[1.01]"
                    onClick={() => {
                      window.location.href = `/services/${service.slug}`;
                    }}
                  >
                    <EditButton
                      collection="services"
                      id={service.id}
                      position="top-right"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 border border-gray-200 shadow-sm z-20"
                      isAuthenticated={isAuthenticated}
                    />

                    <div className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${
                      index % 4 === 0 ? 'from-olive-50 to-olive-100' :
                      index % 4 === 1 ? 'from-blue-50 to-blue-100' :
                      index % 4 === 2 ? 'from-green-50 to-green-100' :
                      'from-amber-50 to-amber-100'
                    } shadow-md hover:shadow-lg transition-all duration-300`}>

                      <div className={`flex ${isEven && hasImage ? 'flex-row' : hasImage ? 'flex-row-reverse' : 'flex-col'} ${!hasImage ? 'min-h-[140px] lg:min-h-[160px] items-center justify-center' : ''}`}>

                        {hasImage && (
                          <div className="w-full lg:w-1/3 relative overflow-hidden">
                            <div className="aspect-[4/3] lg:aspect-[3/2]">
                              <img
                                src={`${import.meta.env.PUBLIC_API_URL}/api/files/services/${service.id}/${service.image}`}
                                alt={service.name}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                              />
                            </div>
                          </div>
                        )}

                        <div className={`${hasImage ? 'w-full lg:w-2/3' : 'w-full'} p-4 lg:p-5 xl:p-6 flex flex-col justify-center`}>
                          <div className="flex items-start justify-between mb-3 lg:mb-4">
                            <div className="flex-1 pr-4">
                              <h2 className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 leading-tight mb-2 lg:mb-3">
                                {service.name}
                              </h2>

                              {service.is_featured && (
                                <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold mb-2 lg:mb-3 ${
                                  index % 4 === 0 ? 'bg-olive-200 text-olive-800' :
                                  index % 4 === 1 ? 'bg-blue-200 text-blue-800' :
                                  index % 4 === 2 ? 'bg-green-200 text-green-800' :
                                  'bg-amber-200 text-amber-800'
                                }`}>
                                  <Star className="w-4 h-4 mr-2 fill-current" />
                                  ТОП
                                </div>
                              )}
                            </div>

                            {/* Врач справа с фотографией */}
                            {service.expand?.doctors && service.expand.doctors.length > 0 && (
                              <div className="flex-shrink-0 ml-4">
                                <a
                                  href={`/specialists/${service.expand.doctors[0].slug || service.expand.doctors[0].id}`}
                                  className="block group"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <div className="flex items-center gap-3 lg:gap-4">
                                    <div className="text-right">
                                      <div className="text-sm lg:text-base text-gray-500 mb-1">Врач</div>
                                      <div className="text-base lg:text-lg font-medium text-gray-900 group-hover:text-olive-700 transition-colors">
                                        {`${service.expand.doctors[0].surname} ${service.expand.doctors[0].name.charAt(0)}.${service.expand.doctors[0].patronymic ? ` ${service.expand.doctors[0].patronymic.charAt(0)}.` : ''}`}
                                      </div>
                                      <div className="text-sm lg:text-base text-gray-500">
                                        {service.expand.doctors[0].position}
                                      </div>
                                      {service.expand.doctors.length > 1 && (
                                        <div className="text-sm text-gray-400 mt-1">
                                          +{service.expand.doctors.length - 1} врач{service.expand.doctors.length - 1 === 1 ? '' : service.expand.doctors.length - 1 < 5 ? 'а' : 'ей'}
                                        </div>
                                      )}
                                    </div>
                                    <div className="relative">
                                      <div className="w-14 h-14 lg:w-16 lg:h-16 rounded-full overflow-hidden border-2 border-white shadow-md group-hover:shadow-lg transition-all duration-300">
                                        <img
                                          src={service.expand.doctors[0].photo
                                            ? `${import.meta.env.PUBLIC_API_URL}/api/files/doctors/${service.expand.doctors[0].id}/${service.expand.doctors[0].photo}`
                                            : '/placeholder.svg?height=48&width=48'
                                          }
                                          alt={`${service.expand.doctors[0].surname} ${service.expand.doctors[0].name}`}
                                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                                        />
                                      </div>
                                      <div className={`absolute -bottom-1 -right-1 w-5 h-5 lg:w-6 lg:h-6 rounded-full border-2 border-white ${
                                        index % 4 === 0 ? 'bg-olive-500' :
                                        index % 4 === 1 ? 'bg-blue-500' :
                                        index % 4 === 2 ? 'bg-green-500' :
                                        'bg-amber-500'
                                      }`}></div>
                                    </div>
                                  </div>
                                </a>
                              </div>
                            )}
                          </div>

                          {service.short_description && (
                            <div
                              className="text-gray-700 mb-3 lg:mb-4 text-lg lg:text-xl leading-relaxed line-clamp-2 font-medium"
                              dangerouslySetInnerHTML={{ __html: service.short_description }}
                            />
                          )}

                          <div className={`inline-flex items-center text-base lg:text-lg font-semibold mb-2 lg:mb-3 ${
                            index % 4 === 0 ? 'text-olive-700' :
                            index % 4 === 1 ? 'text-blue-700' :
                            index % 4 === 2 ? 'text-green-700' :
                            'text-amber-700'
                          }`}>
                            <span className="mr-3 text-lg">📋</span>
                            {service.expand?.category?.name}
                          </div>

                          <div className="mt-2 lg:mt-3 text-gray-500 text-base lg:text-lg font-medium">
                            Нажмите для подробной информации →
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          ) : (
            <motion.div
              className="text-center py-8 lg:py-12"
              initial={{ opacity: 0, y: 60 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="max-w-2xl mx-auto">
                <div className="text-5xl lg:text-6xl mb-6">🔍</div>
                <h3 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                  {searchQuery
                    ? 'Услуги не найдены'
                    : specialist
                      ? 'У данного специалиста пока нет услуг'
                      : 'Услуги загружаются'
                  }
                </h3>
                <p className="text-gray-600 text-xl lg:text-2xl leading-relaxed font-medium">
                  {searchQuery
                    ? 'Попробуйте изменить поисковый запрос или посмотрите все наши услуги.'
                    : specialist
                      ? 'Возможно, информация об услугах обновляется. Обратитесь к администратору.'
                      : 'Пожалуйста, подождите, пока загрузятся данные об услугах.'
                  }
                </p>
                {searchQuery && (
                  <button
                    type="button"
                    className="mt-8 text-olive-600 text-lg lg:text-xl hover:text-olive-800 transition-colors bg-transparent border-none cursor-pointer font-semibold"
                    onClick={() => setSearchQuery('')}
                  >
                    ← Показать все услуги
                  </button>
                )}
              </div>
            </motion.div>
          )}
        </div>
      </section>

      {/* Simple Call to Action */}
      {!specialist && filteredServices.length > 0 && (
        <section className="bg-gradient-to-r from-olive-600 to-olive-700 text-white py-6 lg:py-8">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <h2 className="text-3xl lg:text-4xl xl:text-5xl font-bold mb-4 lg:mb-5">
              Готовы к лечению?
            </h2>
            <p className="text-xl lg:text-2xl mb-5 lg:mb-6 opacity-90 max-w-3xl mx-auto leading-relaxed font-medium">
              Запишитесь на консультацию к нашим специалистам. Мы поможем вам обрести здоровую и красивую улыбку.
            </p>
            <div className="text-2xl lg:text-3xl font-bold mb-2">
              📞 +7 (8152) 52-57-08
            </div>
            <div className="text-lg lg:text-xl opacity-80 font-medium">
              Звоните прямо сейчас или запишитесь онлайн
            </div>
          </div>
        </section>
      )}
    </div>
  );
};
