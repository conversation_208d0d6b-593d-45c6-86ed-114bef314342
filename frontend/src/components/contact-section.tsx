export default function ContactSection() {
  return (
    <section className="relative py-20 md:py-32">
      <div className="absolute inset-0 bg-gradient-to-br from-olive-50 via-white to-olive-50/50"></div>
      <div className="absolute inset-0  bg-center opacity-[0.05]"></div>

      <div className="container relative z-10 mx-auto px-4 md:px-6">
        <div className="mx-auto max-w-3xl text-center">
          <div className="inline-flex items-center rounded-full bg-gradient-to-r from-olive-100/80 to-olive-200/80 px-4 py-2 text-sm font-medium text-olive-700 backdrop-blur-sm shadow-lg border border-olive-200/50">
            <span className="mr-2 block h-2 w-2 rounded-full bg-gradient-to-r from-olive-500 to-olive-600"></span>
            Свяжитесь с нами
          </div>
          <h2 className="mt-4 text-3xl font-bold text-olive-900 md:text-4xl">
            Готовы к{" "}
            <span className="bg-gradient-to-r from-olive-600 to-olive-500 bg-clip-text text-transparent">
              улыбке будущего
            </span>
            ?
          </h2>
          <p className="mt-4 text-olive-700">
            Оставьте свои контактные данные, и мы свяжемся с вами, как только сайт будет запущен
          </p>

          <div className="mt-6">
            <a
              href="/faq"
              className="inline-flex items-center text-olive-600 hover:text-olive-700 font-medium transition-colors"
            >
              Есть вопросы? Посмотрите наш FAQ
              <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>

          <div className="mt-8 overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-olive-100/80 p-1 backdrop-blur-sm">
            <div className="rounded-lg bg-white/60 p-6">
              <form className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label htmlFor="name" className="mb-2 block text-sm font-medium text-olive-700">
                      Имя
                    </label>
                    <input
                      type="text"
                      id="name"
                      className="w-full rounded-lg border border-olive-200 bg-white/80 px-4 py-2.5 text-olive-900 placeholder-olive-400 focus:border-olive-500 focus:outline-none focus:ring-1 focus:ring-olive-500"
                      placeholder="Ваше имя"
                    />
                  </div>
                  <div>
                    <label htmlFor="phone" className="mb-2 block text-sm font-medium text-olive-700">
                      Телефон
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      className="w-full rounded-lg border border-olive-200 bg-white/80 px-4 py-2.5 text-olive-900 placeholder-olive-400 focus:border-olive-500 focus:outline-none focus:ring-1 focus:ring-olive-500"
                      placeholder="+7 (___) ___-____"
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="email" className="mb-2 block text-sm font-medium text-olive-700">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="w-full rounded-lg border border-olive-200 bg-white/80 px-4 py-2.5 text-olive-900 placeholder-olive-400 focus:border-olive-500 focus:outline-none focus:ring-1 focus:ring-olive-500"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label htmlFor="message" className="mb-2 block text-sm font-medium text-olive-700">
                    Сообщение
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    className="w-full rounded-lg border border-olive-200 bg-white/80 px-4 py-2.5 text-olive-900 placeholder-olive-400 focus:border-olive-500 focus:outline-none focus:ring-1 focus:ring-olive-500"
                    placeholder="Ваше сообщение..."
                  ></textarea>
                </div>
                <div>
                  <button
                    type="submit"
                    className="group relative w-full overflow-hidden rounded-lg bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 px-4 py-3 text-center font-medium text-white transition-all shadow-xl hover:shadow-2xl hover:scale-105"
                  >
                    <span className="relative z-10">Отправить сообщение</span>
                    <span className="absolute inset-0 -z-0 translate-y-full bg-gradient-to-r from-olive-500 to-olive-400 transition-transform duration-300 group-hover:translate-y-0"></span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

