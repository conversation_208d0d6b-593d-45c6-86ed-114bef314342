import { useState } from "react"
import { Leaf, Clock, Shield, Zap, Microscope, HeartPulse } from "lucide-react"

export default function FeaturesSection() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)

  const features = [
    {
      icon: Microscope,
      title: "Передовые технологии",
      description:
        "Используем инновационное оборудование и материалы последнего поколения для лечения наших пациентов.",
    },
    {
      icon: Clock,
      title: "Удобное расписание",
      description: "Гибкий график работы и возможность онлайн-записи на прием в удобное для вас время.",
    },
    {
      icon: Shield,
      title: "Безопасность",
      description: "Строгое соблюдение всех санитарных норм и использование стерильных инструментов для вашей защиты.",
    },
    {
      icon: HeartPulse,
      title: "Забота о пациентах",
      description: "Индивидуальный подход к каждому пациенту и комфортная атмосфера во время лечения.",
    },
    {
      icon: Zap,
      title: "Быстрые результаты",
      description: "Эффективные методики лечения, позволяющие достичь желаемых результатов в кратчайшие сроки.",
    },
    {
      icon: Leaf,
      title: "Экологичность",
      description: "Использование безопасных для окружающей среды материалов и технологий в нашей практике.",
    },
  ]

  return (
    <section className="relative py-20 md:py-32">
      <div className="absolute inset-0 bg-gradient-to-br from-[#8BC34A]/5 via-white to-[#8BC34A]/5"></div>
      <div className="absolute inset-0  bg-center opacity-[0.05]"></div>

      <div className="container relative z-10 mx-auto px-4 md:px-6">
        <div className="mx-auto mb-12 max-w-2xl text-center">
          <div className="inline-flex items-center rounded-full bg-gradient-to-r from-[#8BC34A]/20 to-[#85C026]/20 px-4 py-2 text-sm font-medium text-[#4E8C29] backdrop-blur-sm shadow-lg border border-[#8BC34A]/30">
            <span className="mr-2 block h-2 w-2 rounded-full bg-gradient-to-r from-[#8BC34A] to-[#4E8C29]"></span>
            Почему мы
          </div>
          <h2 className="mt-4 text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 via-[#4E8C29] to-gray-900 bg-clip-text text-transparent">Инновационный подход к стоматологии</h2>
          <p className="mt-4 text-[#4E8C29]">
            Мы сочетаем передовые технологии с индивидуальным подходом к каждому пациенту
          </p>
        </div>

        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-white/80 to-[#8BC34A]/20 p-1 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:shadow-[#8BC34A]/20"
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              <div className="relative z-10 rounded-xl bg-white/60 p-6 transition-all duration-300 group-hover:bg-white/80">
                <div className="mb-4 inline-flex rounded-xl bg-[#8BC34A]/20 p-3 text-[#8BC34A] transition-all duration-300 group-hover:bg-[#8BC34A]/30 group-hover:text-[#4E8C29]">
                  <feature.icon className="h-6 w-6" />
                </div>
                <h3 className="mb-2 text-xl font-bold text-[#4E8C29]">{feature.title}</h3>
                <p className="text-[#4E8C29]">{feature.description}</p>
              </div>

              {/* Animated gradient border */}
              <div
                className={`absolute inset-0 rounded-2xl bg-gradient-to-r from-[#8BC34A] via-[#85C026] to-[#8BC34A] opacity-0 transition-opacity duration-300 ${
                  hoveredIndex === index ? "opacity-100" : ""
                }`}
                style={{
                  backgroundSize: "200% 100%",
                  animation: hoveredIndex === index ? "gradientMove 2s linear infinite" : "none",
                }}
              ></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

