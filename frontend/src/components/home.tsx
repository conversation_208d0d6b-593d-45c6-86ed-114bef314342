import Header from "@/components/header"
import Footer from "@/components/footer"
import HeroSection from "@/components/hero-section"
import FeaturesSection from "@/components/features-section"
import AboutSection from "@/components/about-section"
import ContactSection from "@/components/contact-section"
import VKWidget from "@/components/vk-widget"

import PromosSection from "@/components/promos-section"
import { type Promo, type HtmlBlocks } from "@/lib/api"

interface HomeSectionProps {
    promos: Promo[];
    htmlBlocks?: HtmlBlocks[];
}

export const HomeSection = ({ promos, htmlBlocks = [] }: HomeSectionProps) => {
    // Фильтруем блоки по секциям
    const heroBlocks = htmlBlocks.filter(block => block.section === 'hero');
    return (
        <div className="relative flex min-h-screen flex-col bg-gradient-to-b from-[#8BC34A]/5 via-[#8BC34A]/10 to-[#8BC34A]/5">
            {/* Background elements */}
            <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
                <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-[#8BC34A]/30 blur-[100px]" />
                <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-[#85C026]/30 blur-[80px]" />
                <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-[#8BC34A]/20 blur-[120px]" />
            </div>

            {/* Grid lines */}
            <div className="pointer-events-none absolute inset-0 z-0  bg-center opacity-[0.05]" />

            {/* <Header /> */}
            <main className="relative z-10 flex-1">
                <HeroSection htmlBlocks={heroBlocks} />
                <FeaturesSection />
                <VKWidget />

                {/* <AboutSection /> */}
                <PromosSection promos={promos} />
                {/* <ContactSection /> */}
            </main>
        </div>
    )
}

