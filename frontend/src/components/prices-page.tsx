import { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  SearchIcon,
  ArrowUpDownIcon,
  StarIcon,
  XIcon,
  FilterIcon,
  CheckCircleIcon,
  PhoneIcon,
  CalendarIcon
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { EditButton } from '@/components/admin/EditButton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Drawer, DrawerContent, DrawerTrigger } from '@/components/ui/drawer';
import { cn } from '@/lib/utils';

interface Price {
  id: string;
  name: string;
  price: number;
  price_prefix?: string;
  price_suffix?: string;
  unit?: string;
  is_active?: boolean;
  sort_order?: number;
  category?: string;
  service?: string;
  expand?: {
    category?: {
      id: string;
      name: string;
    };
    service?: {
      id: string;
      name: string;
    };
  };
}

interface PricesPageProps {
  prices: Price[];
  categories: any[];
  isAuthenticated?: boolean;
}

export const PricesPage = ({ prices, categories, isAuthenticated = false }: PricesPageProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc' | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [isMobileView, setIsMobileView] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 768);
      if (window.innerWidth < 768 && viewMode === 'table') {
        setViewMode('cards');
      }
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [viewMode]);

  // Фильтрация
  const filteredPrices = useMemo(() => {
    return prices.filter(price => {
      const matchesSearch = price.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || price.expand?.category?.id === selectedCategory;
      return matchesSearch && matchesCategory && price.is_active !== false;
    });
  }, [prices, searchTerm, selectedCategory]);

  // Сортировка
  const sortedPrices = useMemo(() => {
    if (!sortOrder) return filteredPrices;
    return [...filteredPrices].sort((a, b) => sortOrder === 'asc' ? a.price - b.price : b.price - a.price);
  }, [filteredPrices, sortOrder]);

  // Группировка по категориям
  const pricesByCategory = useMemo(() => {
    return categories.map(category => {
      const categoryPrices = sortedPrices.filter(price => price.expand?.category?.id === category.id);
      return { ...category, prices: categoryPrices };
    }).filter(category => category.prices.length > 0);
  }, [categories, sortedPrices]);

  const formatPrice = (price: Price) => {
    let formatted = '';
    if (price.price_prefix) formatted += price.price_prefix + ' ';
    formatted += price.price.toLocaleString('ru-RU');
    if (price.price_suffix) formatted += ' ' + price.price_suffix;
    else formatted += ' ₽';
    if (price.unit) formatted += ' / ' + price.unit;
    return formatted;
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-olive-50 via-white to-olive-50/50">
      {/* Фоновые элементы */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <motion.div animate={{ x: [0, 30, 0], y: [0, -20, 0], scale: [1, 1.1, 1] }} transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }} className="absolute -left-[10%] top-[20%] h-[400px] w-[400px] rounded-full bg-gradient-to-r from-olive-300/20 to-olive-400/30 blur-[120px]" />
        <motion.div animate={{ x: [0, -25, 0], y: [0, 15, 0], scale: [1, 0.9, 1] }} transition={{ duration: 25, repeat: Infinity, ease: 'easeInOut', delay: 5 }} className="absolute -right-[5%] top-[40%] h-[300px] w-[300px] rounded-full bg-gradient-to-l from-olive-400/25 to-olive-500/20 blur-[100px]" />
        <motion.div animate={{ x: [0, 20, 0], y: [0, -30, 0], scale: [1, 1.2, 1] }} transition={{ duration: 30, repeat: Infinity, ease: 'easeInOut', delay: 10 }} className="absolute bottom-[20%] left-[20%] h-[350px] w-[350px] rounded-full bg-gradient-to-tr from-olive-300/15 to-olive-200/25 blur-[140px]" />
      </div>
      <div className="pointer-events-none absolute inset-0 z-0 bg-center opacity-[0.05]" />
      <div className="relative z-10 container mx-auto px-4 py-8 md:py-16">
        {/* Заголовок и фильтры */}
        <div className="mx-auto max-w-3xl text-center mb-12">
          <Badge variant="flat">Прайс-лист</Badge>
          <h1 className="mb-4 text-3xl md:text-4xl font-bold tracking-tight text-olive-900">Цены на стоматологические услуги</h1>
          <p className="text-lg text-gray-600">Прозрачное ценообразование и доступные цены на все виды стоматологических услуг</p>
        </div>
        <div className="flex flex-col md:flex-row gap-3 md:gap-4 items-center justify-center max-w-3xl mx-auto mb-10">
          <div className="relative w-full md:w-72">
            <Input
              type="text"
              placeholder="Поиск услуги по названию..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-10 h-12 text-base border-olive-200 focus:border-olive-400"
              aria-label="Поиск услуг"
            />
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            {searchTerm && (
              <button onClick={() => setSearchTerm('')} className="absolute right-3 top-1/2 -translate-y-1/2 h-6 w-6 text-gray-400 hover:text-gray-600 focus:outline-none" aria-label="Очистить поиск">
                <XIcon className="h-5 w-5" />
              </button>
            )}
          </div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="border-olive-200 text-olive-700 flex items-center gap-2">
                <FilterIcon className="h-4 w-4" />
                Категория
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-2">
              <Button variant={selectedCategory === 'all' ? 'default' : 'ghost'} className="w-full mb-1" onClick={() => setSelectedCategory('all')}>Все категории</Button>
              {categories.map(category => (
                <Button key={category.id} variant={selectedCategory === category.id ? 'default' : 'ghost'} className="w-full mb-1" onClick={() => setSelectedCategory(category.id)}>{category.name}</Button>
              ))}
            </PopoverContent>
          </Popover>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="border-olive-200 text-olive-700 flex items-center gap-2">
                <ArrowUpDownIcon className="h-4 w-4" />
                {sortOrder === 'asc' ? 'Сначала дешевле' : sortOrder === 'desc' ? 'Сначала дороже' : 'Сортировка'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-44 p-2">
              <Button variant={!sortOrder ? 'default' : 'ghost'} className="w-full mb-1" onClick={() => setSortOrder(null)}>По умолчанию</Button>
              <Button variant={sortOrder === 'asc' ? 'default' : 'ghost'} className="w-full mb-1" onClick={() => setSortOrder('asc')}>Сначала дешевле</Button>
              <Button variant={sortOrder === 'desc' ? 'default' : 'ghost'} className="w-full" onClick={() => setSortOrder('desc')}>Сначала дороже</Button>
            </PopoverContent>
          </Popover>
        </div>
        {/* Таблица/карточки с ценами */}
        <div className="mt-8">
          {pricesByCategory.length > 0 ? (
            <div className="space-y-10">
              {pricesByCategory.map((category, idx) => (
                <section key={category.id} className="bg-white/80 rounded-2xl shadow-lg border-0 overflow-hidden">
                  <div className="px-6 pt-6 pb-2 flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                    <div>
                      <div className="text-xs uppercase tracking-wide text-olive-500 font-semibold mb-2">Категория</div>
                      <h2 className="text-2xl md:text-3xl font-bold text-olive-900 mb-2">{category.name}</h2>
                      {category.description && (
                        <div className="text-base text-olive-700 mb-4 prose prose-olive max-w-none" dangerouslySetInnerHTML={{ __html: category.description }} />
                      )}
                    </div>
                    {/* Элементы администрирования для категории */}
                    <div className="flex gap-2 items-center mt-2 md:mt-0">
                      <EditButton collection="service_categories" id={category.id} position="inline" size="sm" isAuthenticated={isAuthenticated} />
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full border-separate border-spacing-0">
                      <thead>
                        <tr className="bg-olive-50">
                          <th className="text-left py-3 px-6 font-semibold text-olive-700 text-base">Наименование услуги</th>
                          <th className="text-right py-3 px-6 font-semibold text-olive-700 text-base">Стоимость</th>
                          <th className="w-16"></th>
                        </tr>
                      </thead>
                      <tbody>
                        {(category.prices as Price[]).map((price: Price) => (
                          <tr key={price.id} className="border-b border-olive-100 hover:bg-olive-50 group">
                            <td className="py-4 px-6 align-top">
                              <div className="font-medium text-olive-900 text-base leading-snug flex items-center gap-2">
                                {price.name}
                                {/* Элемент администрирования для услуги */}
                                <EditButton collection="prices" id={price.id} position="inline" size="sm" isAuthenticated={isAuthenticated} />
                              </div>
                              {price.expand?.service?.name && (
                                <div className="text-xs text-olive-500 mt-1">{price.expand.service.name}</div>
                              )}
                            </td>
                            <td className="py-4 px-6 text-right align-top">
                              <div className="font-semibold text-lg text-olive-700">{formatPrice(price)}</div>
                            </td>
                            <td className="py-4 px-6 align-top"></td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </section>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-olive-100 rounded-full flex items-center justify-center">
                <SearchIcon className="h-10 w-10 text-olive-500" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{searchTerm ? 'Услуги не найдены' : 'Цены загружаются'}</h3>
              <p className="text-gray-600 mb-6">{searchTerm ? 'Попробуйте изменить поисковый запрос или выбрать другую категорию' : 'Пожалуйста, подождите...'}</p>
              {searchTerm && (
                <Button variant="outline" onClick={() => setSearchTerm('')} className="border-olive-200 text-olive-700 hover:bg-olive-50">
                  <XIcon className="h-4 w-4 mr-2" />Очистить поиск
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Информационные карточки */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-white/90 border-0 shadow-lg rounded-2xl overflow-hidden">
            <CardHeader className="bg-olive-50/80 pb-2 border-b-0">
              <CardTitle className="text-lg font-semibold flex items-center gap-2 text-olive-800"><CheckCircleIcon className="h-5 w-5 text-olive-600" />Важная информация</CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <ul className="space-y-2 text-base text-olive-700">
                <li className="flex items-start gap-2"><CheckCircleIcon className="h-4 w-4 text-olive-500 mt-0.5 flex-shrink-0" />Цены на сайте носят информационный характер</li>
                <li className="flex items-start gap-2"><CheckCircleIcon className="h-4 w-4 text-olive-500 mt-0.5 flex-shrink-0" />Окончательная стоимость определяется после консультации</li>
                <li className="flex items-start gap-2"><CheckCircleIcon className="h-4 w-4 text-olive-500 mt-0.5 flex-shrink-0" />В клинике действуют специальные предложения</li>
              </ul>
            </CardContent>
          </Card>
          <Card className="bg-white/90 border-0 shadow-lg rounded-2xl overflow-hidden">
            <CardHeader className="bg-olive-50/80 pb-2 border-b-0">
              <CardTitle className="text-lg font-semibold flex items-center gap-2 text-olive-800"><StarIcon className="h-5 w-5 text-olive-600" />Скидки и акции</CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <ul className="space-y-2 text-base text-olive-700">
                <li className="flex items-start gap-2"><CheckCircleIcon className="h-4 w-4 text-olive-500 mt-0.5 flex-shrink-0" />Скидка 10% на первое посещение</li>
                <li className="flex items-start gap-2"><CheckCircleIcon className="h-4 w-4 text-olive-500 mt-0.5 flex-shrink-0" />Семейная скидка 15% на все услуги</li>
                <li className="flex items-start gap-2"><CheckCircleIcon className="h-4 w-4 text-olive-500 mt-0.5 flex-shrink-0" />Специальные цены для постоянных клиентов</li>
              </ul>
            </CardContent>
          </Card>
          <Card className="bg-white/90 border-0 shadow-lg rounded-2xl overflow-hidden">
            <CardHeader className="bg-olive-50/80 pb-2 border-b-0">
              <CardTitle className="text-lg font-semibold flex items-center gap-2 text-olive-800"><PhoneIcon className="h-5 w-5 text-olive-600" />Консультация</CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <p className="text-base text-olive-700 mb-4">Нужна консультация? Наши специалисты помогут подобрать оптимальный план лечения.</p>
              <div className="flex flex-col gap-2">
                <Button size="sm" className="w-full bg-gradient-to-r from-olive-500 to-olive-600 text-white shadow-lg hover:from-olive-600 hover:to-olive-700"><CalendarIcon className="h-4 w-4 mr-2" />Записаться</Button>
                <Button size="sm" variant="outline" className="w-full border-olive-200 text-olive-700"><PhoneIcon className="h-4 w-4 mr-2" />Позвонить</Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA */}
        <div className="mt-20">
          <div className="bg-gradient-to-r from-olive-600 to-olive-700 rounded-2xl p-8 md:p-12 shadow-xl text-center md:text-left flex flex-col md:flex-row md:items-center md:justify-between gap-8">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-3">Нужна консультация по ценам?</h3>
              <p className="text-white/90 mb-0 text-lg">Наши специалисты помогут подобрать оптимальный план лечения с учетом вашего бюджета</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-end">
              <Button size="lg" className="bg-white text-olive-700 hover:bg-white/90 shadow-xl font-semibold text-lg px-8 py-4">
                <CalendarIcon className="h-5 w-5 mr-2" />Записаться на консультацию
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 shadow-lg font-semibold text-lg px-8 py-4">
                <PhoneIcon className="h-5 w-5 mr-2" />Позвонить сейчас
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
