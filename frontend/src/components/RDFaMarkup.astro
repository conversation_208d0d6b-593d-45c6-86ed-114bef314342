---
// RDFa разметка для Яндекса и других поисковых систем
export interface Props {
  type?: 'organization' | 'service' | 'doctor' | 'article' | 'faq' | 'review';
  data?: any;
}

const { type = 'organization', data } = Astro.props;
---

{type === 'organization' && (
  <div vocab="http://schema.org/" typeof="Dentist" style="display: none;">
    <span property="name">STOM-LINE</span>
    <span property="description">Стоматологическая клиника STOM-LINE в Мурманске - современное оборудование, профессиональные врачи и комфортное лечение зубов</span>
    <span property="url">https://stom-line.ru</span>
    <span property="telephone">+7 (8152) 52-57-08</span>
    <span property="email"><EMAIL></span>
    
    <div property="address" typeof="PostalAddress">
      <span property="streetAddress">ул. Полярные Зори, д. 35/2</span>
      <span property="addressLocality">Мурманск</span>
      <span property="addressRegion">Мурманская область</span>
      <span property="postalCode">183038</span>
      <span property="addressCountry">RU</span>
    </div>
    
    <div property="geo" typeof="GeoCoordinates">
      <span property="latitude">68.9585</span>
      <span property="longitude">33.0827</span>
    </div>
    
    <span property="openingHours">Mo-Fr 09:00-20:00</span>
    <span property="openingHours">Sa 09:00-18:00</span>
    <span property="priceRange">₽₽</span>
  </div>
)}

{type === 'service' && data && (
  <div vocab="http://schema.org/" typeof="MedicalProcedure" style="display: none;">
    <span property="name">{data.name}</span>
    <span property="description">{data.short_description || data.content}</span>
    <span property="url">https://stom-line.ru/services/{data.slug}</span>
    
    <div property="provider" typeof="Dentist">
      <span property="name">STOM-LINE</span>
      <span property="url">https://stom-line.ru</span>
    </div>
    
    {data.expand?.category && (
      <span property="medicalSpecialty">{data.expand.category.name}</span>
    )}
  </div>
)}

{type === 'doctor' && data && (
  <div vocab="http://schema.org/" typeof="Person" style="display: none;">
    <span property="name">{`${data.surname} ${data.name} ${data.patronymic || ''}`.trim()}</span>
    <span property="jobTitle">{data.position}</span>
    <span property="description">{data.short_description}</span>
    <span property="url">https://stom-line.ru/specialists/{data.slug}</span>
    
    <div property="worksFor" typeof="Dentist">
      <span property="name">STOM-LINE</span>
      <span property="url">https://stom-line.ru</span>
    </div>
    
    {data.expand?.specializations?.map((spec: any) => (
      <span property="medicalSpecialty">{spec.name}</span>
    ))}
  </div>
)}

{type === 'article' && data && (
  <div vocab="http://schema.org/" typeof="Article" style="display: none;">
    <span property="headline">{data.title}</span>
    <span property="description">{data.meta_description || data.subtitle}</span>
    <span property="url">https://stom-line.ru/{data.type || 'news'}/{data.slug}</span>
    <span property="datePublished">{data.date || data.created}</span>
    <span property="dateModified">{data.updated}</span>
    
    <div property="author" typeof="Organization">
      <span property="name">STOM-LINE</span>
      <span property="url">https://stom-line.ru</span>
    </div>
    
    <div property="publisher" typeof="Organization">
      <span property="name">STOM-LINE</span>
      <span property="url">https://stom-line.ru</span>
    </div>
  </div>
)}

{type === 'faq' && data && Array.isArray(data) && (
  <div vocab="http://schema.org/" typeof="FAQPage" style="display: none;">
    {data.map((faq: any) => (
      <div property="mainEntity" typeof="Question">
        <span property="name">{faq.question}</span>
        <div property="acceptedAnswer" typeof="Answer">
          <span property="text">{faq.answer}</span>
        </div>
      </div>
    ))}
  </div>
)}

{type === 'review' && data && Array.isArray(data) && (
  <div vocab="http://schema.org/" style="display: none;">
    {data.map((review: any) => (
      <div typeof="Review">
        <span property="author">{review.author}</span>
        <span property="datePublished">{review.date}</span>
        <span property="reviewBody">{review.content}</span>
        {review.title && <span property="name">{review.title}</span>}
        
        <div property="itemReviewed" typeof="Dentist">
          <span property="name">STOM-LINE</span>
        </div>
      </div>
    ))}
  </div>
)}
