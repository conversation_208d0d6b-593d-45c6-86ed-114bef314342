'use client'

import { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'
import { AuroraText } from './magicui/aurora-text'
import { CallbackForm } from './callback-form'
import type { HtmlBlocks } from '@/lib/api'
import VKWidget from './vk-widget'

interface HeroSectionProps {
  htmlBlocks?: HtmlBlocks[]
}

export default function HeroSection({ htmlBlocks = [] }: HeroSectionProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  // Функция для получения контента блока по ключу
  const getBlockContent = (key: string, fallback: string = '') => {
    const block = htmlBlocks.find((b) => b.key === key)
    return block?.content || fallback
  }

  // Получаем контент для различных элементов
  const badge = getBlockContent('hero_badge', 'Сайт в разработке')
  const title = getBlockContent('hero_title', 'Стоматология')
  const titleAccent = getBlockContent('hero_title_accent', 'Будущего')
  const description = getBlockContent(
    'hero_description',
    'Скоро здесь будет представлена информация о наших инновационных стоматологических услугах. Следите за обновлениями!'
  )

  useEffect(() => {
    setIsVisible(true)
  }, [])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    const particles: Particle[] = []
    const particleCount = 50

    class Particle {
      x: number
      y: number
      size: number
      speedX: number
      speedY: number
      color: string

      constructor() {
        this.x = Math.random() * canvas.width
        this.y = Math.random() * canvas.height
        this.size = Math.random() * 3 + 1
        this.speedX = Math.random() * 0.5 - 0.25
        this.speedY = Math.random() * 0.5 - 0.25
        this.color = `rgba(127, 134, 67, ${Math.random() * 0.3 + 0.1})`
      }

      update() {
        this.x += this.speedX
        this.y += this.speedY

        if (this.x > canvas.width) this.x = 0
        else if (this.x < 0) this.x = canvas.width

        if (this.y > canvas.height) this.y = 0
        else if (this.y < 0) this.y = canvas.height
      }

      draw() {
        ctx.fillStyle = this.color
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
        ctx.fill()
      }
    }

    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle())
    }

    function connectParticles() {
      for (let i = 0; i < particles.length; i++) {
        for (let j = i; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x
          const dy = particles[i].y - particles[j].y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 100) {
            ctx.beginPath()
            ctx.strokeStyle = `rgba(127, 134, 67, ${0.1 - distance / 1000})`
            ctx.lineWidth = 0.5
            ctx.moveTo(particles[i].x, particles[i].y)
            ctx.lineTo(particles[j].x, particles[j].y)
            ctx.stroke()
          }
        }
      }
    }

    function animate() {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      for (const particle of particles) {
        particle.update()
        particle.draw()
      }

      connectParticles()
      requestAnimationFrame(animate)
    }

    animate()

    const handleResize = () => {
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <section className='relative min-h-[80vh] overflow-hidden py-12 sm:min-h-[90vh] sm:py-20 md:py-32'>
      <style>{`
        @keyframes gradient-border {
          0%, 100% { border-radius: 37% 29% 27% 27% / 28% 25% 41% 37%; }
          25% { border-radius: 47% 29% 39% 49% / 61% 19% 66% 26%; }
          50% { border-radius: 57% 23% 47% 72% / 63% 17% 66% 33%; }
          75% { border-radius: 28% 49% 29% 100% / 93% 20% 64% 25%; }
        }
        @keyframes gradient-1 {
          0%, 100% { top: 0; right: 0; }
          50% { top: 50%; right: 25%; }
          75% { top: 25%; right: 50%; }
        }
        @keyframes gradient-2 {
          0%, 100% { top: 0; left: 0; }
          60% { top: 75%; left: 25%; }
          85% { top: 50%; left: 50%; }
        }
        @keyframes gradient-3 {
          0%, 100% { bottom: 0; left: 0; }
          40% { bottom: 50%; left: 25%; }
          65% { bottom: 25%; left: 50%; }
        }
        @keyframes gradient-4 {
          0%, 100% { bottom: 0; right: 0; }
          50% { bottom: 25%; right: 40%; }
          90% { bottom: 50%; right: 25%; }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        @keyframes pulse-glow {
          0%, 100% { box-shadow: 0 0 20px rgba(139, 195, 74, 0.3); }
          50% { box-shadow: 0 0 40px rgba(139, 195, 74, 0.6); }
        }
        @keyframes sparkle {
          0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
          50% { opacity: 1; transform: scale(1) rotate(180deg); }
        }
        @keyframes shimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }
        .bg-gradient-radial {
          background: radial-gradient(circle, var(--tw-gradient-stops));
        }
      `}</style>
      <canvas ref={canvasRef} className='absolute inset-0 z-0 h-full w-full'></canvas>
      <div className='relative z-10 container mx-auto px-4 md:px-6'>
        {/* Мобильная версия с измененным порядком */}
        <div className='flex flex-col gap-6 md:hidden'>
          {/* 1. Блок с анимированным градиентом (первый на мобильных) */}
          <motion.div
            className='flex items-center justify-center'
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 50 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          >
            <div className='relative aspect-square w-full max-w-[240px] sm:max-w-[280px]'>
              <motion.div
                className='absolute inset-0 flex items-center justify-center overflow-hidden rounded-full border border-[#8BC34A]/20 shadow-2xl'
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 1, delay: 0.3, type: 'spring', stiffness: 100 }}
                style={{
                  animation: 'float 3s ease-in-out infinite, pulse-glow 2s ease-in-out infinite',
                  backgroundImage: 'url(/logo.png)',
                  backgroundSize: '90%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'center',
                  opacity: 0.9
                }}
              >
                {/* Прозрачный оверлей с градиентом */}
                <div className='absolute inset-0 bg-gradient-to-br from-[#8BC34A]/30 to-[#85C026]/30 backdrop-blur-sm'></div>

                {/* Плавающий градиент с улучшенной видимостью */}
                <span className='pointer-events-none absolute inset-0 overflow-hidden rounded-full'>
                  {/* Основные градиентные пятна с увеличенной яркостью */}
                  <span className='pointer-events-none absolute -top-1/2 -left-1/2 h-[200%] w-[200%] animate-[gradient-border_6s_ease-in-out_infinite,gradient-1_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#8BC34A]/70 via-[#8BC34A]/40 to-transparent blur-[2rem]'></span>
                  <span className='pointer-events-none absolute -top-1/2 -right-1/2 h-[200%] w-[200%] animate-[gradient-border_6s_ease-in-out_infinite,gradient-2_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#4E8C29]/70 via-[#4E8C29]/40 to-transparent blur-[2rem]'></span>
                  <span className='pointer-events-none absolute -bottom-1/2 -left-1/2 h-[200%] w-[200%] animate-[gradient-border_6s_ease-in-out_infinite,gradient-3_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#85C026]/70 via-[#85C026]/40 to-transparent blur-[2rem]'></span>
                  <span className='pointer-events-none absolute -right-1/2 -bottom-1/2 h-[200%] w-[200%] animate-[gradient-border_6s_ease-in-out_infinite,gradient-4_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#4E8C29]/70 via-[#4E8C29]/40 to-transparent blur-[2rem]'></span>

                  {/* Дополнительные светящиеся эффекты */}
                  <span className='bg-gradient-radial pointer-events-none absolute top-1/4 left-1/4 h-[50%] w-[50%] animate-[gradient-border_4s_ease-in-out_infinite,gradient-1_8s_ease-in-out_infinite_alternate] rounded-full from-[#8BC34A]/60 to-transparent blur-[1.5rem]'></span>
                  <span className='bg-gradient-radial pointer-events-none absolute top-1/3 right-1/3 h-[40%] w-[40%] animate-[gradient-border_5s_ease-in-out_infinite,gradient-2_10s_ease-in-out_infinite_alternate] rounded-full from-[#85C026]/60 to-transparent blur-[1.5rem]'></span>

                  {/* Контрастный внешний ободок */}
                  <span className='pointer-events-none absolute inset-2 animate-pulse rounded-full border-2 border-[#8BC34A]/30'></span>
                </span>

                <div className='relative h-[95%] w-[95%] overflow-hidden rounded-full p-1 shadow-2xl shadow-[#8BC34A]/20'>
                  <div className='absolute inset-0 flex items-center justify-center text-gray-800'>
                    <div className='relative'>
                      <div className='relative z-10 flex h-full flex-col items-center justify-center space-y-2 text-center sm:space-y-3'>

                        {/* Название клиники */}
                        <motion.div className='' initial={{ opacity: 0, x: -50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.8, delay: 0.6 }}>
                          <h1 className='text-3xl mt-5 font-bold tracking-tight sm:text-4xl'>
                            <motion.span
                              className='from-olive-200 via-olive-300 to-olive-200 relative inline-flex overflow-hidden bg-gradient-to-r bg-clip-text text-olive-100 font-bold'
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ duration: 1, delay: 0.8 }}
                            >
                              Stom Line
                            </motion.span>
                          </h1>
                        </motion.div>

                        {/* Подзаголовок */}
                        <p className='text-olive-100 relative overflow-hidden text-sm font-bold sm:text-base'>
                          <span className='relative z-10 block px-4 py-2'>Ваша идеальная улыбка начинается здесь</span>
                        </p>

                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
              {/* Улучшенные светящиеся эффекты */}
              <div className='absolute -bottom-4 -left-4 h-16 w-16 animate-pulse rounded-full bg-[#8BC34A]/60 blur-3xl sm:h-20 sm:w-20'></div>
              <div
                className='absolute -top-4 -right-4 h-16 w-16 animate-pulse rounded-full bg-[#85C026]/60 blur-3xl sm:h-20 sm:w-20'
                style={{ animationDelay: '1s' }}
              ></div>
              <div
                className='absolute top-1/2 left-1/2 h-28 w-28 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-[#4E8C29]/30 blur-3xl sm:h-32 sm:w-32'
                style={{ animationDelay: '0.5s' }}
              ></div>

              {/* Дополнительные акцентные точки */}
              <div
                className='absolute top-1/4 right-1/4 h-8 w-8 animate-pulse rounded-full bg-[#8BC34A]/50 blur-2xl sm:h-12 sm:w-12'
                style={{ animationDelay: '2s' }}
              ></div>
              <div
                className='absolute bottom-1/4 left-1/4 h-8 w-8 animate-pulse rounded-full bg-[#85C026]/50 blur-2xl sm:h-12 sm:w-12'
                style={{ animationDelay: '1.5s' }}
              ></div>
            </div>
          </motion.div>

          {/* 2. Hero section с текстом (второй на мобильных) */}
          <div className='flex flex-col justify-center space-y-4 text-center'>
            <div className='mx-auto inline-flex items-center rounded-full border border-[#8BC34A]/30 bg-gradient-to-r from-[#8BC34A]/20 to-[#85C026]/20 px-4 py-2 text-xs font-medium text-gray-800 shadow-lg backdrop-blur-sm sm:text-sm'>
              <span className='mr-2 block h-2 w-2 rounded-full bg-gradient-to-r from-[#8BC34A] to-[#4E8C29]'></span>
              {badge}
            </div>
            <div className='space-y-3 sm:space-y-4'>
              <h1 className='text-3xl font-bold tracking-tighter text-gray-900 sm:text-4xl'>
                {title}{' '}
                <div className='mt-2 bg-gradient-to-r from-[#4E8C29] to-[#8BC34A] bg-clip-text text-transparent sm:mt-3'>
                  <AuroraText speed={2}>{titleAccent}</AuroraText>
                </div>
              </h1>
              <p className='mx-auto max-w-[600px] text-base text-gray-800 sm:text-lg'>{description}</p>
            </div>
          </div>

          {/* 3. Форма обратной связи (третья на мобильных) */}
          <div className='flex justify-center'>
            <CallbackForm
              variant="expanded"
              title="Запишитесь на консультацию"
              description="Оставьте заявку и мы свяжемся с вами в течение 15 минут"
              className="w-full max-w-[600px]"
            />
          </div>
        </div>

        {/* Десктопная версия с оригинальным порядком */}
        <div className='hidden md:grid md:grid-cols-2 md:gap-10'>
          <div className='flex flex-col justify-center space-y-4 text-center sm:space-y-6 md:text-left'>
            <div className='mx-auto inline-flex items-center rounded-full border border-[#8BC34A]/30 bg-gradient-to-r from-[#8BC34A]/20 to-[#85C026]/20 px-4 py-2 text-xs font-medium text-gray-800 shadow-lg backdrop-blur-sm sm:text-sm md:mx-0'>
              <span className='mr-2 block h-2 w-2 rounded-full bg-gradient-to-r from-[#8BC34A] to-[#4E8C29]'></span>
              {badge}
            </div>
            <div className='space-y-3 sm:space-y-4'>
              <h1 className='text-3xl font-bold tracking-tighter text-gray-900 sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl'>
                {title}{' '}
                <div className='mt-2 bg-gradient-to-r from-[#4E8C29] to-[#8BC34A] bg-clip-text text-transparent sm:mt-3'>
                  {/* <TextGenerateEffect className="text-olive-600 text-5xl" words={titleAccent} /> */}
                  <AuroraText speed={2}>{titleAccent}</AuroraText>
                </div>
              </h1>
              <p className='mx-auto max-w-[600px] text-base text-gray-800 sm:text-lg md:mx-0 md:text-xl'>{description}</p>
            </div>
            <div className='flex justify-center md:justify-start'>
              <CallbackForm
                variant="expanded"
                title="Запишитесь на консультацию"
                description="Оставьте заявку и мы свяжемся с вами в течение 15 минут"
                className="w-full max-w-[600px]"
              />
            </div>
          </div>
          <motion.div
            className='mt-6 flex items-center justify-center md:mt-0'
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 50 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          >
            <div className='relative aspect-square w-full max-w-[240px] sm:max-w-[320px] md:max-w-[400px]'>
              <motion.div
                className='absolute inset-0 flex items-center justify-center overflow-hidden rounded-full border border-[#8BC34A]/20 shadow-2xl'
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 1, delay: 0.3, type: 'spring', stiffness: 100 }}
                style={{
                  animation: 'float 3s ease-in-out infinite, pulse-glow 2s ease-in-out infinite',
                  backgroundImage: 'url(/logo.png)',
                  backgroundSize: '90%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'center',
                  opacity: 0.9
                }}
              >
                {/* Прозрачный оверлей с градиентом */}
                <div className='absolute inset-0 bg-gradient-to-br from-[#8BC34A]/30 to-[#85C026]/30 backdrop-blur-sm'></div>

                {/* Плавающий градиент с улучшенной видимостью */}
                <span className='pointer-events-none absolute inset-0 overflow-hidden rounded-full'>
                  {/* Основные градиентные пятна с увеличенной яркостью */}
                  <span className='pointer-events-none absolute -top-1/2 -left-1/2 h-[200%] w-[200%] animate-[gradient-border_6s_ease-in-out_infinite,gradient-1_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#8BC34A]/70 via-[#8BC34A]/40 to-transparent blur-[2rem]'></span>
                  <span className='pointer-events-none absolute -top-1/2 -right-1/2 h-[200%] w-[200%] animate-[gradient-border_6s_ease-in-out_infinite,gradient-2_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#4E8C29]/70 via-[#4E8C29]/40 to-transparent blur-[2rem]'></span>
                  <span className='pointer-events-none absolute -bottom-1/2 -left-1/2 h-[200%] w-[200%] animate-[gradient-border_6s_ease-in-out_infinite,gradient-3_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#85C026]/70 via-[#85C026]/40 to-transparent blur-[2rem]'></span>
                  <span className='pointer-events-none absolute -right-1/2 -bottom-1/2 h-[200%] w-[200%] animate-[gradient-border_6s_ease-in-out_infinite,gradient-4_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#4E8C29]/70 via-[#4E8C29]/40 to-transparent blur-[2rem]'></span>

                  {/* Дополнительные светящиеся эффекты */}
                  <span className='bg-gradient-radial pointer-events-none absolute top-1/4 left-1/4 h-[50%] w-[50%] animate-[gradient-border_4s_ease-in-out_infinite,gradient-1_8s_ease-in-out_infinite_alternate] rounded-full from-[#8BC34A]/60 to-transparent blur-[1.5rem]'></span>
                  <span className='bg-gradient-radial pointer-events-none absolute top-1/3 right-1/3 h-[40%] w-[40%] animate-[gradient-border_5s_ease-in-out_infinite,gradient-2_10s_ease-in-out_infinite_alternate] rounded-full from-[#85C026]/60 to-transparent blur-[1.5rem]'></span>

                  {/* Контрастный внешний ободок */}
                  <span className='pointer-events-none absolute inset-2 animate-pulse rounded-full border-2 border-[#8BC34A]/30'></span>
                </span>

                <div className='relative h-[95%] w-[95%] overflow-hidden rounded-full p-1 shadow-2xl shadow-[#8BC34A]/20'>
                  <div className='absolute inset-0 flex items-center justify-center text-gray-800'>
                    <div className='relative'>
                      <div className='relative z-10 flex h-full flex-col items-center justify-center space-y-2 text-center sm:space-y-3'>

                        {/* Название клиники */}
                        <motion.div className='' initial={{ opacity: 0, x: -50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.8, delay: 0.6 }}>
                          <h1 className='text-4xl mt-5 font-bold tracking-tight md:text-6xl lg:text-7xl'>
                            <motion.span
                              className='from-olive-200 via-olive-300 to-olive-200 relative inline-flex overflow-hidden bg-gradient-to-r bg-clip-text text-olive-100 font-bold'
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ duration: 1, delay: 0.8 }}
                            >
                              Stom Line
                            </motion.span>
                          </h1>
                        </motion.div>

                        {/* Подзаголовок */}

                        <p className='text-olive-100 relative overflow-hidden text-lg font-bold md:text-xl lg:text-2xl'>
                          <span className='relative z-10 block px-4 py-2'>Ваша идеальная улыбка начинается здесь</span>
                        </p>

                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
              {/* Улучшенные светящиеся эффекты */}
              <div className='absolute -bottom-4 -left-4 h-20 w-20 animate-pulse rounded-full bg-[#8BC34A]/60 blur-3xl sm:h-28 sm:w-28'></div>
              <div
                className='absolute -top-4 -right-4 h-20 w-20 animate-pulse rounded-full bg-[#85C026]/60 blur-3xl sm:h-28 sm:w-28'
                style={{ animationDelay: '1s' }}
              ></div>
              <div
                className='absolute top-1/2 left-1/2 h-36 w-36 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-[#4E8C29]/30 blur-3xl sm:h-44 sm:w-44'
                style={{ animationDelay: '0.5s' }}
              ></div>

              {/* Дополнительные акцентные точки */}
              <div
                className='absolute top-1/4 right-1/4 h-12 w-12 animate-pulse rounded-full bg-[#8BC34A]/50 blur-2xl sm:h-16 sm:w-16'
                style={{ animationDelay: '2s' }}
              ></div>
              <div
                className='absolute bottom-1/4 left-1/4 h-12 w-12 animate-pulse rounded-full bg-[#85C026]/50 blur-2xl sm:h-16 sm:w-16'
                style={{ animationDelay: '1.5s' }}
              ></div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
