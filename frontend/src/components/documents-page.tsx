import { useState, useEffect, useRef } from "react"
import { X, FileText, Download, Eye, Calendar, Building2, Shield, Award, FileCheck, Gavel, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { EditButton } from "@/components/admin/EditButton"
import type { Files } from "@/lib/pocketbase-types"

interface DocumentsPageProps {
  documents?: Files[];
  isAuthenticated?: boolean;
}

// Типы документов медицинской организации согласно российскому законодательству
const documentTypes = {
  license: {
    title: "Лицензии",
    icon: Shield,
    description: "Лицензии на осуществление медицинской деятельности",
    color: "bg-blue-500",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  certificate: {
    title: "Сертификаты",
    icon: Award,
    description: "Сертификаты соответствия и качества",
    color: "bg-green-500",
    bgColor: "bg-green-50",
    borderColor: "border-green-200"
  },
  permit: {
    title: "Разрешения",
    icon: FileCheck,
    description: "Разрешения и допуски к медицинской деятельности",
    color: "bg-purple-500",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  },
  regulation: {
    title: "Нормативные документы",
    icon: Gavel,
    description: "Внутренние положения и регламенты",
    color: "bg-orange-500",
    bgColor: "bg-orange-50",
    borderColor: "border-orange-200"
  },
  staff: {
    title: "Документы персонала",
    icon: Users,
    description: "Квалификационные документы специалистов",
    color: "bg-indigo-500",
    bgColor: "bg-indigo-50",
    borderColor: "border-indigo-200"
  },
  other: {
    title: "Прочие документы",
    icon: FileText,
    description: "Другие документы организации",
    color: "bg-gray-500",
    bgColor: "bg-gray-50",
    borderColor: "border-gray-200"
  }
}

// Типы документов для фильтрации
const documentTypeMap: Record<string, keyof typeof documentTypes> = {
  'certificate': 'certificate',
  'document': 'license',
  'license': 'license',
  'permit': 'permit',
  'regulation': 'regulation',
  'staff': 'staff'
}

// Custom hook for intersection observer
const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
      },
      { threshold: 0.1, ...options }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [])

  return [ref, isIntersecting] as const
}

export default function DocumentsPage({ documents = [], isAuthenticated = false }: DocumentsPageProps) {
  const [selectedDocument, setSelectedDocument] = useState<Files | null>(null)
  const [selectedType, setSelectedType] = useState<string>("all")
  const [heroRef, heroInView] = useIntersectionObserver()
  const [statsRef, statsInView] = useIntersectionObserver()

  // Фильтруем документы по типу certificate и document
  const validDocuments = documents.filter(doc =>
    doc.type === 'certificate' || doc.type === 'document'
  )

  // Фильтрация документов по выбранному типу
  const filteredDocuments = selectedType === "all"
    ? validDocuments
    : validDocuments.filter(doc => {
        const mappedType = documentTypeMap[doc.type || '']
        return mappedType === selectedType
      })

  // Статистика по документам
  const documentStats = {
    total: validDocuments.length,
    active: validDocuments.length, // Все документы считаем активными
    licenses: validDocuments.filter(doc => doc.type === 'document').length,
    certificates: validDocuments.filter(doc => doc.type === 'certificate').length
  }

  // Функция для получения URL файла
  const getFileUrl = (document: Files) => {
    if (!document.files) return ''
    const fileName = Array.isArray(document.files) ? document.files[0] : document.files
    return `https://pb.stom-line.ru/api/files/files/${document.id}/${fileName}`
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-olive-50 via-white to-olive-50">
      {/* Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]"></div>
        <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]"></div>
        <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]"></div>
      </div>

      {/* Grid lines */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]"></div>

      <div className="relative z-10">
        {/* Hero Section */}
        <section ref={heroRef} className="pt-20 pb-16">
          <div className="container mx-auto px-4">
            <div className={`text-center transition-all duration-1000 ${
              heroInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <div className="inline-flex items-center gap-2 bg-olive-100 text-olive-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Building2 className="w-4 h-4" />
                Документы медицинской организации
              </div>
              
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Документы и{" "}
                <span className="bg-gradient-to-r from-olive-600 to-olive-400 bg-clip-text text-transparent">
                  лицензии
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
                Полный перечень документов, подтверждающих право на осуществление медицинской деятельности 
                и соответствие требованиям российского законодательства
              </p>

              {isAuthenticated && (
                <div className="mb-8">
                  <EditButton 
                    collection="files" 
                    recordId="new"
                    className="bg-olive-600 hover:bg-olive-700 text-white"
                  >
                    Добавить документ
                  </EditButton>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Statistics Section */}
        <section ref={statsRef} className="py-16 bg-white/50 backdrop-blur-sm">
          <div className="container mx-auto px-4">
            <div className={`transition-all duration-1000 ${
              statsInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
                <div className="text-center">
                  <div className="bg-olive-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FileText className="w-8 h-8 text-olive-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{documentStats.total}</div>
                  <div className="text-gray-600">Всего документов</div>
                </div>

                <div className="text-center">
                  <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FileCheck className="w-8 h-8 text-green-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{documentStats.active}</div>
                  <div className="text-gray-600">Действующих</div>
                </div>

                <div className="text-center">
                  <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{documentStats.licenses}</div>
                  <div className="text-gray-600">Лицензий</div>
                </div>

                <div className="text-center">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="w-8 h-8 text-purple-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{documentStats.certificates}</div>
                  <div className="text-gray-600">Сертификатов</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Document Types Filter */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Категории документов
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Выберите категорию для просмотра соответствующих документов
              </p>
            </div>

            <div className="flex flex-wrap justify-center gap-4 mb-12">
              <Button
                variant={selectedType === "all" ? "default" : "outline"}
                onClick={() => setSelectedType("all")}
                className={selectedType === "all" ? "bg-olive-600 hover:bg-olive-700" : ""}
              >
                Все документы
              </Button>

              {Object.entries(documentTypes).map(([key, type]) => {
                const Icon = type.icon
                return (
                  <Button
                    key={key}
                    variant={selectedType === key ? "default" : "outline"}
                    onClick={() => setSelectedType(key)}
                    className={`flex items-center gap-2 ${
                      selectedType === key ? "bg-olive-600 hover:bg-olive-700" : ""
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {type.title}
                  </Button>
                )
              })}
            </div>

            {/* Documents Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDocuments.map((document) => {
                const docType = documentTypes[documentTypeMap[document.type || ''] || 'other']
                const Icon = docType.icon
                const fileUrl = getFileUrl(document)

                return (
                  <Card
                    key={document.id}
                    className={`group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1 ${docType.borderColor} border-2`}
                    onClick={() => setSelectedDocument(document)}
                  >
                    <CardHeader className={`${docType.bgColor} pb-4`}>
                      <div className="flex items-start justify-between mb-4">
                        <div className={`${docType.color} w-12 h-12 rounded-lg flex items-center justify-center`}>
                          <Icon className="w-6 h-6 text-white" />
                        </div>
                        <Badge
                          variant="default"
                          className="bg-green-500"
                        >
                          Действующий
                        </Badge>
                      </div>

                      {/* Предварительный просмотр файла */}
                      {fileUrl && (
                        <div className="mb-4">
                          {(() => {
                            const fileName = Array.isArray(document.files) ? document.files[0] : document.files
                            return fileName && fileName.toLowerCase().includes('.pdf') ? (
                              <div className="bg-white rounded-lg p-4 border-2 border-dashed border-gray-200 text-center">
                                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                                <p className="text-xs text-gray-500">PDF документ</p>
                              </div>
                            ) : (
                              <div className="bg-white rounded-lg p-2 border">
                                <img
                                  src={fileUrl}
                                  alt={document.name || 'Документ'}
                                  className="w-full h-32 object-cover rounded"
                                  loading="lazy"
                                />
                              </div>
                            )
                          })()}
                        </div>
                      )}

                      <CardTitle className="text-lg leading-tight group-hover:text-olive-600 transition-colors">
                        {document.name || 'Документ без названия'}
                      </CardTitle>

                      {document.description && (
                        <CardDescription className="text-sm">
                          {document.description}
                        </CardDescription>
                      )}
                    </CardHeader>

                    <CardContent className="pt-4">
                      {/* Закомментированные поля для будущего использования */}
                      {/*
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>Номер:</span>
                          <span className="font-medium">{document.number}</span>
                        </div>

                        <div className="flex justify-between">
                          <span>Дата выдачи:</span>
                          <span className="font-medium">
                            {document.issue_date ? new Date(document.issue_date).toLocaleDateString('ru-RU') : 'Не указана'}
                          </span>
                        </div>

                        <div className="flex justify-between">
                          <span>Действует до:</span>
                          <span className="font-medium">{document.valid_until || 'Бессрочно'}</span>
                        </div>

                        <div className="pt-2 border-t">
                          <span className="text-xs text-gray-500">Выдан: {document.issuer || 'Не указан'}</span>
                        </div>
                      </div>
                      */}

                      {/* Привязанный врач */}
                      {document.doctor && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Врач:</h4>
                          <div className="flex items-center gap-2">
                            {document.expand?.doctor ? (
                              <Badge variant="outline" className="text-xs">
                                {document.expand.doctor.surname} {document.expand.doctor.name} {document.expand.doctor.patronymic}
                                {document.expand.doctor.position && ` - ${document.expand.doctor.position}`}
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-xs">
                                Врач ID: {document.doctor}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}

                      <div className="flex gap-2 mt-4">
                        {fileUrl && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1"
                              onClick={(e) => {
                                e.stopPropagation()
                                window.open(fileUrl, '_blank')
                              }}
                            >
                              <Eye className="w-4 h-4 mr-2" />
                              Просмотр
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1"
                              onClick={(e) => {
                                e.stopPropagation()
                                const link = document.createElement('a')
                                link.href = fileUrl
                                link.download = document.name || 'document'
                                link.click()
                              }}
                            >
                              <Download className="w-4 h-4 mr-2" />
                              Скачать
                            </Button>
                          </>
                        )}
                      </div>

                      {isAuthenticated && (
                        <div className="mt-3">
                          <EditButton
                            collection="files"
                            id={document.id}
                            size="sm"
                            variant="text"
                            className="w-full"
                          >
                            Редактировать
                          </EditButton>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {/* Сообщение если нет документов */}
            {filteredDocuments.length === 0 && (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {selectedType === "all" ? "Документы не найдены" : "Нет документов данного типа"}
                </h3>
                <p className="text-gray-500">
                  {selectedType === "all"
                    ? "Документы будут отображены после их добавления в систему"
                    : "Попробуйте выбрать другую категорию документов"
                  }
                </p>
              </div>
            )}
          </div>
        </section>

        {/* Legal Information Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Правовая информация
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Gavel className="w-5 h-5 text-olive-600" />
                      Нормативная база
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Федеральный закон № 323-ФЗ "Об основах охраны здоровья граждан"</li>
                      <li>• Федеральный закон № 99-ФЗ "О лицензировании отдельных видов деятельности"</li>
                      <li>• Постановление Правительства РФ № 291 о лицензировании медицинской деятельности</li>
                      <li>• СанПиН 2.1.3.2630-10 "Санитарно-эпидемиологические требования"</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5 text-olive-600" />
                      Контролирующие органы
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Министерство здравоохранения РФ</li>
                      <li>• Департамент здравоохранения г. Москвы</li>
                      <li>• Роспотребнадзор</li>
                      <li>• Росздравнадзор</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-8 p-6 bg-olive-50 rounded-lg border border-olive-200">
                <h3 className="font-semibold text-gray-900 mb-2">Важная информация</h3>
                <p className="text-sm text-gray-600">
                  Все документы представлены в соответствии с требованиями российского законодательства.
                  Медицинская организация ООО "Стом-Лайн" имеет все необходимые разрешения для осуществления
                  медицинской деятельности. Документы регулярно обновляются и проходят проверку контролирующими органами.
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Document Modal */}
      {selectedDocument && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {selectedDocument.name || 'Документ без названия'}
                  </h3>
                  {selectedDocument.description && (
                    <p className="text-gray-600">{selectedDocument.description}</p>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedDocument(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>

              <div className="space-y-4">
                {/* Закомментированные поля для будущего использования */}
                {/*
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Номер документа</label>
                    <p className="text-lg font-semibold text-gray-900">{selectedDocument.number || 'Не указан'}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500">Статус</label>
                    <Badge variant="default" className="bg-green-500 mt-1">
                      Действующий
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Дата выдачи</label>
                    <p className="text-gray-900 flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      {selectedDocument.issue_date ? new Date(selectedDocument.issue_date).toLocaleDateString('ru-RU') : 'Не указана'}
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500">Действует до</label>
                    <p className="text-gray-900">{selectedDocument.valid_until || 'Бессрочно'}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Орган, выдавший документ</label>
                  <p className="text-gray-900">{selectedDocument.issuer || 'Не указан'}</p>
                </div>
                */}

                {/* Привязанный врач */}
                {selectedDocument.doctor && (
                  <div>
                    <label className="text-sm font-medium text-gray-500 mb-2 block">Врач</label>
                    <div className="flex flex-wrap gap-2">
                      {selectedDocument.expand?.doctor ? (
                        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          {selectedDocument.expand.doctor.photo && (
                            <img
                              src={`https://pb.stom-line.ru/api/files/doctors/${selectedDocument.expand.doctor.id}/${selectedDocument.expand.doctor.photo}`}
                              alt={`${selectedDocument.expand.doctor.surname} ${selectedDocument.expand.doctor.name}`}
                              className="w-12 h-12 rounded-full object-cover"
                            />
                          )}
                          <div>
                            <p className="font-medium text-gray-900">
                              {selectedDocument.expand.doctor.surname} {selectedDocument.expand.doctor.name} {selectedDocument.expand.doctor.patronymic}
                            </p>
                            {selectedDocument.expand.doctor.position && (
                              <p className="text-sm text-gray-600">{selectedDocument.expand.doctor.position}</p>
                            )}
                          </div>
                        </div>
                      ) : (
                        <Badge variant="outline">
                          Врач ID: {selectedDocument.doctor}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Предварительный просмотр документа */}
                {selectedDocument.files && (
                  <div>
                    <label className="text-sm font-medium text-gray-500 mb-2 block">Предварительный просмотр</label>
                    <div className="border rounded-lg p-4 bg-gray-50">
                      {(() => {
                        const fileName = Array.isArray(selectedDocument.files) ? selectedDocument.files[0] : selectedDocument.files
                        return fileName.toLowerCase().includes('.pdf') ? (
                          <div className="text-center py-8">
                            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-600">PDF документ</p>
                            <p className="text-sm text-gray-500">Нажмите "Просмотреть" для открытия</p>
                          </div>
                        ) : (
                          <img
                            src={getFileUrl(selectedDocument)}
                            alt={selectedDocument.name || 'Документ'}
                            className="max-w-full h-auto rounded-lg shadow-sm"
                            style={{ maxHeight: '400px', margin: '0 auto', display: 'block' }}
                          />
                        )
                      })()}
                    </div>
                  </div>
                )}

                <div className="pt-4 border-t">
                  <div className="flex gap-3">
                    {selectedDocument.files && (
                      <>
                        <Button
                          className="flex-1 bg-olive-600 hover:bg-olive-700"
                          onClick={() => window.open(getFileUrl(selectedDocument), '_blank')}
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          Просмотреть документ
                        </Button>
                        <Button
                          variant="outline"
                          className="flex-1"
                          onClick={() => {
                            const link = document.createElement('a')
                            link.href = getFileUrl(selectedDocument)
                            link.download = selectedDocument.name || 'document'
                            link.click()
                          }}
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Скачать файл
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
