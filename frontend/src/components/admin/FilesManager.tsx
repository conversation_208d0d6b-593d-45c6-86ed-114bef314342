import * as React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  FileText, 
  Image, 
  Award, 
  FolderOpen, 
  Search, 
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Plus
} from 'lucide-react';
import { getCollectionRecords, deleteRecord } from '@/lib/pocketbase-admin';

interface FileRecord {
  id: string;
  name: string;
  type: 'certificate' | 'any' | 'image' | 'document';
  files: string[];
  doctor?: string;
  created: string;
  updated: string;
}

interface FilesManagerProps {
  pbUrl: string;
}

export const FilesManager: React.FC<FilesManagerProps> = ({ pbUrl }) => {
  const [files, setFiles] = React.useState<FileRecord[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [typeFilter, setTypeFilter] = React.useState<string>('all');
  const [error, setError] = React.useState('');

  const loadFiles = React.useCallback(async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('pb_token');
      if (!token) return;

      const result = await getCollectionRecords('files', token, 1, 100, '-created');
      setFiles(result.items || []);
    } catch (err) {
      console.error('Ошибка загрузки файлов:', err);
      setError('Не удалось загрузить файлы');
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    loadFiles();
  }, [loadFiles]);

  const filteredFiles = React.useMemo(() => {
    return files.filter(file => {
      const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = typeFilter === 'all' || file.type === typeFilter;
      return matchesSearch && matchesType;
    });
  }, [files, searchTerm, typeFilter]);

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'certificate':
        return <Award className="h-5 w-5 text-yellow-500" />;
      case 'image':
        return <Image className="h-5 w-5 text-blue-500" />;
      case 'document':
        return <FileText className="h-5 w-5 text-green-500" />;
      default:
        return <FolderOpen className="h-5 w-5 text-gray-500" />;
    }
  };

  const getFileTypeBadge = (type: string) => {
    const badges = {
      certificate: { label: 'Сертификат', className: 'bg-yellow-100 text-yellow-800' },
      image: { label: 'Изображение', className: 'bg-blue-100 text-blue-800' },
      document: { label: 'Документ', className: 'bg-green-100 text-green-800' },
      any: { label: 'Файл', className: 'bg-gray-100 text-gray-800' }
    };
    
    const badge = badges[type as keyof typeof badges] || badges.any;
    return <Badge className={badge.className}>{badge.label}</Badge>;
  };

  const handleDelete = async (id: string, name: string) => {
    if (!confirm(`Вы уверены, что хотите удалить файл "${name}"?`)) return;

    try {
      const token = localStorage.getItem('pb_token');
      if (!token) return;

      await deleteRecord('files', id, token);
      await loadFiles(); // Перезагружаем список
    } catch (err) {
      console.error('Ошибка удаления файла:', err);
      alert('Не удалось удалить файл');
    }
  };

  const getFileUrl = (record: FileRecord, filename: string) => {
    return `${pbUrl}/api/files/files/${record.id}/${filename}`;
  };

  const fileTypeStats = React.useMemo(() => {
    const stats = {
      certificate: 0,
      image: 0,
      document: 0,
      any: 0
    };
    
    files.forEach(file => {
      stats[file.type as keyof typeof stats]++;
    });
    
    return stats;
  }, [files]);

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#8BC34A]"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Статистика */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-yellow-500" />
              <div>
                <div className="text-2xl font-bold">{fileTypeStats.certificate}</div>
                <div className="text-sm text-gray-600">Сертификаты</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{fileTypeStats.document}</div>
                <div className="text-sm text-gray-600">Документы</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Image className="h-5 w-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{fileTypeStats.image}</div>
                <div className="text-sm text-gray-600">Изображения</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FolderOpen className="h-5 w-5 text-gray-500" />
              <div>
                <div className="text-2xl font-bold">{files.length}</div>
                <div className="text-sm text-gray-600">Всего файлов</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Фильтры и поиск */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <FolderOpen className="mr-2 h-5 w-5" />
              Управление файлами
            </div>
            <Button className="bg-[#8BC34A] hover:bg-[#4E8C29]">
              <Plus className="h-4 w-4 mr-2" />
              Добавить файл
            </Button>
          </CardTitle>
          <CardDescription>
            Управление документами, сертификатами и изображениями
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Поиск файлов..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full md:w-48">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Тип файла" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Все типы</SelectItem>
                  <SelectItem value="certificate">Сертификаты</SelectItem>
                  <SelectItem value="document">Документы</SelectItem>
                  <SelectItem value="image">Изображения</SelectItem>
                  <SelectItem value="any">Прочие файлы</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {error && (
            <div className="text-red-600 text-sm mb-4 p-3 bg-red-50 rounded-lg">
              {error}
            </div>
          )}

          {/* Список файлов */}
          <div className="space-y-3">
            {filteredFiles.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FolderOpen className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                <p>Файлы не найдены</p>
              </div>
            ) : (
              filteredFiles.map((file) => (
                <div
                  key={file.id}
                  className="p-4 border rounded-lg hover:shadow-md transition-shadow bg-white"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      {getFileTypeIcon(file.type)}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-medium text-gray-900">{file.name || 'Без названия'}</h3>
                          {getFileTypeBadge(file.type)}
                        </div>
                        
                        <div className="text-sm text-gray-600 mb-2">
                          Файлов: {file.files?.length || 0}
                        </div>
                        
                        {file.files && file.files.length > 0 && (
                          <div className="flex flex-wrap gap-2 mb-2">
                            {file.files.slice(0, 3).map((filename, index) => (
                              <a
                                key={index}
                                href={getFileUrl(file, filename)}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded hover:bg-gray-200"
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                {filename}
                              </a>
                            ))}
                            {file.files.length > 3 && (
                              <span className="text-xs text-gray-500">
                                +{file.files.length - 3} еще
                              </span>
                            )}
                          </div>
                        )}
                        
                        <div className="text-xs text-gray-500">
                          Создано: {new Date(file.created).toLocaleDateString('ru-RU')}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(file.id, file.name)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
