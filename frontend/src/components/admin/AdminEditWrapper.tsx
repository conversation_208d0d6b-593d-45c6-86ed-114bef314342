import * as React from 'react';
import { PocketbaseLogin } from './PocketbaseLogin';
import { DynamicEditForm } from './DynamicEditForm';

interface AdminEditWrapperProps {
  collection: string;
  id: string;
  schema: any;
  record: any;
  pbUrl: string;
}

export const AdminEditWrapper: React.FC<AdminEditWrapperProps> = ({
  collection,
  id,
  schema,
  record,
  pbUrl,
}) => {
  const [token, setToken] = React.useState<string | null>(() =>
    typeof window !== 'undefined' ? localStorage.getItem('pb_token') : null
  );

  if (!token) {
    return <PocketbaseLogin onLogin={setToken} />;
  }

  return (
    <DynamicEditForm
      collection={collection}
      id={id}
      schema={schema}
      record={record}
      pbUrl={pbUrl}
      token={token}
    />
  );
};
