import * as React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TiptapEditorWrapper } from './TiptapEditorWrapper';

interface Field {
  id: string;
  name: string;
  type: string;
  required?: boolean;
  options?: {
    maxSelect?: number;
    collectionId?: string;
    [key: string]: any;
  };
  collectionId?: string;
  maxSelect?: number;
  [key: string]: any;
}

interface FieldRendererProps {
  field: Field;
  value: any;
  onChange: (value: any) => void;
  relationOptions?: Array<{ id: string; [key: string]: any }>;
  pbUrl: string;
  collection: string;
  recordId: string;
  disabled?: boolean;
}

export const FieldRenderer: React.FC<FieldRendererProps> = ({
  field,
  value,
  onChange,
  relationOptions = [],
  pbUrl,
  collection,
  recordId,
  disabled = false,
}) => {
  const fieldId = `field-${field.name}`;
  const isRequired = field.required || false;
  const [validationError, setValidationError] = React.useState<string>('');

  // Валидация поля
  const validateField = (fieldValue: any): string => {
    // Проверка обязательности
    if (isRequired && (fieldValue === null || fieldValue === undefined || fieldValue === '')) {
      return 'Это поле обязательно для заполнения';
    }

    // Валидация по типу поля
    switch (field.type) {
      case 'email':
        if (fieldValue && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(fieldValue)) {
          return 'Введите корректный email адрес';
        }
        break;

      case 'url':
        if (fieldValue && !/^https?:\/\/.+/.test(fieldValue)) {
          return 'Введите корректный URL (начинающийся с http:// или https://)';
        }
        break;

      case 'number':
        if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
          const numValue = Number(fieldValue);
          if (isNaN(numValue)) {
            return 'Введите корректное число';
          }
          if (field.min !== undefined && numValue < field.min) {
            return `Значение должно быть не менее ${field.min}`;
          }
          if (field.max !== undefined && numValue > field.max) {
            return `Значение должно быть не более ${field.max}`;
          }
        }
        break;

      case 'text':
        if (fieldValue) {
          if (field.min && fieldValue.length < field.min) {
            return `Минимальная длина: ${field.min} символов`;
          }
          if (field.max && fieldValue.length > field.max) {
            return `Максимальная длина: ${field.max} символов`;
          }
          if (field.pattern && !new RegExp(field.pattern).test(fieldValue)) {
            return 'Значение не соответствует требуемому формату';
          }
        }
        break;

      case 'date':
        if (fieldValue) {
          const date = new Date(fieldValue);
          if (isNaN(date.getTime())) {
            return 'Введите корректную дату';
          }
          if (field.min && date < new Date(field.min)) {
            return `Дата должна быть не ранее ${new Date(field.min).toLocaleDateString('ru-RU')}`;
          }
          if (field.max && date > new Date(field.max)) {
            return `Дата должна быть не позднее ${new Date(field.max).toLocaleDateString('ru-RU')}`;
          }
        }
        break;
    }

    return '';
  };

  // Обработчик изменения с валидацией
  const handleChange = (newValue: any) => {
    const error = validateField(newValue);
    setValidationError(error);
    onChange(newValue);
  };

  // Валидация при изменении значения
  React.useEffect(() => {
    const error = validateField(value);
    setValidationError(error);
  }, [value, field]);
  
  // Функция для получения читаемого названия поля
  const getFieldLabel = () => {
    // Мапинг названий полей на русский
    const labelMap: Record<string, string> = {
      name: 'Название',
      title: 'Заголовок',
      surname: 'Фамилия',
      patronymic: 'Отчество',
      slug: 'URL слаг',
      position: 'Должность',
      short_description: 'Краткое описание',
      biography: 'Биография',
      content: 'Содержимое',
      description: 'Описание',
      photo: 'Фото',
      image: 'Изображение',
      featured_image: 'Главное изображение',
      gallery: 'Галерея',
      specializations: 'Специализации',
      services: 'Услуги',
      category: 'Категория',
      service: 'Услуга',
      experience: 'Опыт работы',
      clinic: 'Клиника',
      meta_title: 'SEO заголовок',
      meta_description: 'SEO описание',
      is_featured: 'Рекомендуемый',
      is_published: 'Опубликовано',
      sort_order: 'Порядок сортировки',
      date: 'Дата',
      question: 'Вопрос',
      answer: 'Ответ',
      price: 'Цена',
      price_prefix: 'Префикс цены',
      price_suffix: 'Суффикс цены',
      parent_slug: 'Родительская страница',
    };
    
    return labelMap[field.name] || field.name;
  };

  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <Input
            id={fieldId}
            type="text"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            placeholder={`Введите ${getFieldLabel().toLowerCase()}`}
            className={validationError ? 'border-red-500' : ''}
          />
        );

      case 'email':
        return (
          <Input
            id={fieldId}
            type="email"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            placeholder="<EMAIL>"
            className={validationError ? 'border-red-500' : ''}
          />
        );

      case 'url':
        return (
          <Input
            id={fieldId}
            type="url"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            placeholder="https://example.com"
            className={validationError ? 'border-red-500' : ''}
          />
        );

      case 'number':
        return (
          <Input
            id={fieldId}
            type="number"
            value={value !== undefined && value !== null ? value : ''}
            onChange={(e) => {
              const val = e.target.value;
              handleChange(val === '' ? null : Number(val));
            }}
            disabled={disabled}
            placeholder="0"
            min={field.min}
            max={field.max}
            className={validationError ? 'border-red-500' : ''}
          />
        );

      case 'date':
        // Конвертируем дату в нужный формат для input[type="date"]
        const dateValue = value ? new Date(value).toISOString().split('T')[0] : '';
        return (
          <Input
            id={fieldId}
            type="date"
            value={dateValue}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            min={field.min}
            max={field.max}
            className={validationError ? 'border-red-500' : ''}
          />
        );

      case 'bool':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fieldId}
              checked={!!value}
              onCheckedChange={(checked) => handleChange(checked)}
              disabled={disabled}
            />
            <label htmlFor={fieldId} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {getFieldLabel()}
            </label>
          </div>
        );

      case 'editor':
        return (
          <div className="space-y-2">
            <TiptapEditorWrapper
              id={fieldId}
              value={value || ''}
              onChange={handleChange}
              readOnly={disabled}
              style={{ minHeight: 200 }}
            />
            {field.maxSize && (
              <p className="text-xs text-gray-500">
                Максимальный размер: {field.maxSize} символов
              </p>
            )}
          </div>
        );

      case 'relation':
        // В PocketBase: maxSelect = 1 означает одиночный выбор, maxSelect = 0 или > 1 означает множественный выбор
        const maxSelectValue = field.options?.maxSelect !== undefined ? field.options.maxSelect : field.maxSelect;
        const isMultiple = maxSelectValue === 0 || (maxSelectValue !== undefined && maxSelectValue > 1);
        
        console.log(`Поле ${field.name}: maxSelect=${maxSelectValue}, isMultiple=${isMultiple}`);

        if (isMultiple) {
          // Мультиселект для связей
          const selectedValues = Array.isArray(value) ? value : (value ? [value] : []);
          
          return (
            <div className="space-y-2">
              <div className="border rounded-md p-3 min-h-[100px] max-h-[200px] overflow-y-auto">
                {relationOptions.length === 0 ? (
                  <p className="text-gray-500 text-sm">Нет доступных вариантов</p>
                ) : (
                  <div className="space-y-2">
                    {relationOptions.map((option) => {
                      const optionLabel = option.name || option.title || option.question || option.id;
                      const isSelected = selectedValues.includes(option.id);
                      
                      return (
                        <div key={option.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`${fieldId}-${option.id}`}
                            checked={isSelected}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                handleChange([...selectedValues, option.id]);
                              } else {
                                handleChange(selectedValues.filter(id => id !== option.id));
                              }
                            }}
                            disabled={disabled}
                          />
                          <label 
                            htmlFor={`${fieldId}-${option.id}`}
                            className="text-sm leading-none cursor-pointer"
                          >
                            {optionLabel}
                          </label>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
              <p className="text-xs text-gray-500">
                Выбрано: {selectedValues.length}
              </p>
            </div>
          );
        } else {
          // Одиночный селект
          return (
            <Select
              value={value || '__empty__'}
              onValueChange={(val) => handleChange(val === '__empty__' ? '' : val)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Выберите вариант" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__empty__">Не выбрано</SelectItem>
                {relationOptions.map((option) => {
                  const optionLabel = option.name || option.title || option.question || option.id;
                  return (
                    <SelectItem key={option.id} value={option.id}>
                      {optionLabel}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          );
        }

      case 'file':
        // Поддержка множественных файлов
        const maxSelectFiles = field.maxSelect || field.options?.maxSelect || 1;
        const isMultipleFiles = maxSelectFiles === 0 || maxSelectFiles > 1;

        const hasFiles = value && (Array.isArray(value) ? value.length > 0 : typeof value === 'string');
        const fileList = Array.isArray(value) ? value : (value ? [value] : []);

        return (
          <div className="space-y-3">
            <Input
              id={fieldId}
              type="file"
              multiple={isMultipleFiles}
              onChange={(e) => {
                const files = e.target.files;
                if (files) {
                  if (isMultipleFiles) {
                    handleChange(Array.from(files));
                  } else {
                    handleChange(files[0] || null);
                  }
                }
              }}
              disabled={disabled}
              accept={field.mimeTypes ? field.mimeTypes.join(',') : undefined}
            />

            {hasFiles && (
              <div className="border rounded-lg p-3 bg-gray-50">
                <p className="text-sm text-gray-600 mb-2">
                  {isMultipleFiles ? 'Текущие файлы:' : 'Текущий файл:'}
                </p>
                <div className="space-y-2">
                  {fileList.map((fileName: string, index: number) => {
                    const fileUrl = `${pbUrl}/api/files/${collection}/${recordId}/${fileName}`;
                    const isImage = /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(fileName);

                    return (
                      <div key={index} className="border rounded p-2 bg-white">
                        {isImage ? (
                          <div>
                            <img
                              src={fileUrl}
                              alt={fileName}
                              className="max-w-xs max-h-32 rounded border mb-2"
                            />
                            <a
                              href={fileUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 text-sm"
                            >
                              {fileName}
                            </a>
                          </div>
                        ) : (
                          <a
                            href={fileUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            {fileName} (скачать)
                          </a>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        );

      case 'autodate':
        // Автоматические даты - только для чтения
        const autoDateValue = value ? new Date(value).toLocaleString('ru-RU') : 'Не установлено';
        return (
          <div className="p-3 bg-gray-50 rounded border text-sm text-gray-600">
            {autoDateValue}
          </div>
        );

      case 'password':
        // Поле пароля - специальная обработка
        return (
          <Input
            id={fieldId}
            type="password"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            placeholder="Введите новый пароль"
          />
        );

      case 'json':
        // JSON поле - используем textarea с валидацией
        return (
          <div className="space-y-2">
            <Textarea
              id={fieldId}
              value={typeof value === 'object' ? JSON.stringify(value, null, 2) : (value || '')}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value);
                  handleChange(parsed);
                } catch {
                  // Если JSON невалидный, сохраняем как строку
                  handleChange(e.target.value);
                }
              }}
              disabled={disabled}
              rows={6}
              placeholder="Введите JSON данные"
              className="font-mono text-sm"
            />
            <p className="text-xs text-gray-500">
              Введите валидный JSON. Данные будут автоматически распарсены.
            </p>
          </div>
        );

      case 'select':
        // Поле выбора с предустановленными опциями
        const selectOptions = field.options?.values || [];
        return (
          <Select
            value={value || '__empty__'}
            onValueChange={(val) => handleChange(val === '__empty__' ? '' : val)}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="Выберите вариант" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="__empty__">Не выбрано</SelectItem>
              {selectOptions.map((option: string, index: number) => (
                <SelectItem key={index} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      default:
        // Для неизвестных типов используем textarea
        return (
          <Textarea
            id={fieldId}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            disabled={disabled}
            rows={3}
            placeholder={`Введите ${getFieldLabel().toLowerCase()}`}
          />
        );
    }
  };

  // Для bool полей не показываем отдельный label, так как он встроен в checkbox
  if (field.type === 'bool') {
    return (
      <div className="space-y-2">
        {renderField()}
        {validationError && (
          <p className="text-sm text-red-600">{validationError}</p>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <label htmlFor={fieldId} className="block text-sm font-medium text-gray-700">
        {getFieldLabel()}
        {isRequired && <span className="text-red-500 ml-1">*</span>}
      </label>
      {renderField()}
      {validationError && (
        <p className="text-sm text-red-600">{validationError}</p>
      )}
      {/* Дополнительная информация о поле */}
      {field.type === 'text' && (field.min || field.max) && (
        <p className="text-xs text-gray-500">
          {field.min && field.max
            ? `Длина: ${field.min}-${field.max} символов`
            : field.min
            ? `Минимум: ${field.min} символов`
            : `Максимум: ${field.max} символов`
          }
        </p>
      )}
      {field.type === 'file' && field.mimeTypes && (
        <p className="text-xs text-gray-500">
          Разрешенные типы файлов: {field.mimeTypes.join(', ')}
        </p>
      )}
    </div>
  );
};
