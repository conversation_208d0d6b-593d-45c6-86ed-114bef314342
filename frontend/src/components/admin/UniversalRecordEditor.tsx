import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Save, X, ExternalLink, Loader2 } from 'lucide-react';
import { FieldRenderer } from './FieldRenderer';
import { getRecord, updateRecord, createRecord } from '@/lib/pocketbase-admin';

// Типы для полей из схемы PocketBase
interface Field {
  id: string;
  name: string;
  type: string;
  required?: boolean;
  hidden?: boolean;
  system?: boolean;
  collectionId?: string;
  maxSelect?: number;
  options?: {
    maxSelect?: number;
    collectionId?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

interface CollectionSchema {
  id: string;
  name: string;
  type: string;
  fields: Field[];
}

interface RelationData {
  [fieldName: string]: Array<{ id: string; [key: string]: any }>;
}

interface UniversalRecordEditorProps {
  // Основные параметры
  collection: string;
  recordId: string;
  initialRecord?: Record<string, any>;

  // Настройки отображения
  mode?: 'modal' | 'inline';
  title?: string;
  description?: string;

  // Настройки функциональности
  showQuickFields?: boolean; // Показывать только основные поля для быстрого редактирования
  allowedFields?: string[]; // Ограничить редактирование определенными полями
  isCreateMode?: boolean; // Режим создания новой записи

  // Колбэки
  onSave?: (record: Record<string, any>) => void;
  onCancel?: () => void;
  onClose?: () => void;

  // Технические параметры
  pbUrl?: string;
  token?: string;

  // Управление модальным окном
  isOpen?: boolean;
}

export const UniversalRecordEditor: React.FC<UniversalRecordEditorProps> = ({
  collection,
  recordId,
  initialRecord,
  mode = 'modal',
  title,
  description,
  showQuickFields = false,
  allowedFields,
  isCreateMode = false,
  onSave,
  onCancel,
  onClose,
  pbUrl = 'https://pb.stom-line.ru',
  token,
  isOpen = true,
}) => {
  // Состояние компонента
  const [schema, setSchema] = React.useState<Field[]>([]);
  const [record, setRecord] = React.useState<Record<string, any>>(initialRecord || {});
  const [formData, setFormData] = React.useState<Record<string, any>>(initialRecord || {});
  const [relationData, setRelationData] = React.useState<RelationData>({});
  
  // Состояние загрузки и ошибок
  const [loading, setLoading] = React.useState(false);
  const [saving, setSaving] = React.useState(false);
  const [error, setError] = React.useState('');
  const [success, setSuccess] = React.useState(false);

  // Получение токена из localStorage если не передан
  const authToken = React.useMemo(() => {
    return token || (typeof window !== 'undefined' ? localStorage.getItem('pb_token') : null);
  }, [token]);

  // Конфигурация полей для быстрого редактирования
  const getQuickEditFields = (collectionName: string, allFields: Field[]): Field[] => {
    const quickFieldsConfig: Record<string, string[]> = {
      doctors: ['name', 'surname', 'position', 'experience', 'short_description'],
      services: ['name', 'short_description', 'category'],
      reviews: ['author', 'title', 'content', 'date', 'is_published'],
      faq: ['question', 'answer', 'category', 'is_published'],
      news: ['title', 'slug', 'date', 'content', 'is_featured'],
      promos: ['title', 'subtitle', 'start_date', 'end_date', 'is_active'],
      pages: ['title', 'slug', 'content', 'is_published'],
      prices: ['name', 'price', 'category', 'service', 'is_active'],
      service_categories: ['name', 'slug', 'description'],
      html_blocks: ['content'],
      personal: ['surname', 'name', 'patronymic', 'position', 'about'],
    };

    const quickFieldNames = quickFieldsConfig[collectionName] || ['name', 'title'];
    return allFields.filter(field => quickFieldNames.includes(field.name));
  };

  // Загрузка схемы коллекции
  React.useEffect(() => {
    const fetchSchema = async () => {
      try {
        const { getCollectionFields } = await import('@/lib/pocketbase-schema');

        let visibleFields = await getCollectionFields(collection, {
          includeSystem: false,
          includeHidden: false
        });

        // Фильтрация полей для быстрого редактирования
        if (showQuickFields) {
          visibleFields = getQuickEditFields(collection, visibleFields);
        }

        // Фильтрация по разрешенным полям
        if (allowedFields && allowedFields.length > 0) {
          visibleFields = visibleFields.filter((field: any) =>
            allowedFields.includes(field.name)
          );
        }

        setSchema(visibleFields);
      } catch (err) {
        console.error('Ошибка загрузки схемы:', err);
        setError('Не удалось загрузить схему коллекции');
      }
    };

    fetchSchema();
  }, [collection, showQuickFields, allowedFields]);

  // Загрузка записи если не передана изначально (только для режима редактирования)
  React.useEffect(() => {
    if (!isCreateMode && !initialRecord && recordId && recordId !== 'new' && authToken) {
      loadRecord();
    } else if (isCreateMode) {
      // В режиме создания инициализируем пустую запись
      const emptyRecord = {};
      setRecord(emptyRecord);
      setFormData(emptyRecord);
    }
  }, [recordId, authToken, initialRecord, isCreateMode]);

  const loadRecord = async () => {
    setLoading(true);
    setError('');
    try {
      const data = await getRecord(collection, recordId);
      setRecord(data);
      setFormData(data);
    } catch (err: any) {
      console.error('Error loading record:', err);
      setError(err?.message || 'Ошибка загрузки записи');
    } finally {
      setLoading(false);
    }
  };

  // Загрузка данных для relation полей
  React.useEffect(() => {
    const fetchRelationData = async () => {
      const relationFields = schema.filter(field => field.type === 'relation');
      if (relationFields.length === 0 || !authToken) return;

      const relationPromises = relationFields.map(async (field) => {
        const targetCollection = field.options?.collectionId || field.collectionId;
        
        if (!targetCollection) {
          console.warn(`Не найден targetCollection для поля ${field.name}`);
          return null;
        }
        
        try {
          const response = await fetch(
            `${pbUrl}/api/collections/${targetCollection}/records?perPage=500&sort=created`,
            {
              headers: {
                'Authorization': `Bearer ${authToken}`,
              },
            }
          );

          if (response.ok) {
            const data = await response.json();
            return { fieldName: field.name, records: data.items || [] };
          }
        } catch (err) {
          console.error(`Ошибка загрузки данных для поля ${field.name}:`, err);
        }
        return null;
      });

      const results = await Promise.all(relationPromises);
      const newRelationData: RelationData = {};
      
      results.forEach(result => {
        if (result) {
          newRelationData[result.fieldName] = result.records;
        }
      });

      setRelationData(newRelationData);
    };

    if (schema.length > 0) {
      fetchRelationData();
    }
  }, [schema, authToken, pbUrl]);

  // Обработка изменения полей
  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // Сохранение записи
  const handleSave = async () => {
    if (!authToken) {
      setError('Не найден токен авторизации');
      return;
    }

    setSaving(true);
    setError('');

    try {
      // Подготовка данных для отправки
      const submitData = new FormData();

      schema.forEach(field => {
        const value = formData[field.name];

        if (value !== undefined && value !== null) {
          if (field.type === 'file') {
            // Обработка файлов
            if (value instanceof FileList) {
              Array.from(value).forEach(file => {
                submitData.append(field.name, file);
              });
            } else if (value instanceof File) {
              submitData.append(field.name, value);
            } else if (typeof value === 'string' && value) {
              // Существующий файл - не изменяем (только для режима редактирования)
              if (!isCreateMode) {
                submitData.append(field.name, value);
              }
            }
          } else if (field.type === 'relation') {
            // Обработка связей
            if (Array.isArray(value)) {
              value.forEach(id => submitData.append(field.name, id));
            } else if (value) {
              submitData.append(field.name, value);
            }
          } else {
            // Обычные поля
            submitData.append(field.name, String(value));
          }
        }
      });

      let savedRecord;
      if (isCreateMode) {
        // Создание новой записи
        savedRecord = await createRecord(collection, submitData);
      } else {
        // Обновление существующей записи
        savedRecord = await updateRecord(collection, recordId, submitData);
      }

      setRecord(savedRecord);
      setFormData(savedRecord);
      setSuccess(true);

      // Вызов колбэка
      if (onSave) {
        onSave(savedRecord);
      }

      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      setError(err?.message || 'Ошибка при сохранении');
    } finally {
      setSaving(false);
    }
  };

  // Обработка отмены
  const handleCancel = () => {
    setFormData(record);
    setError('');
    if (onCancel) {
      onCancel();
    }
  };

  // Обработка закрытия
  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  // Генерация заголовка
  const getTitle = () => {
    if (title) return title;
    if (isCreateMode) return 'Создание новой записи';
    if (showQuickFields) return 'Быстрое редактирование';
    return 'Редактирование записи';
  };

  // Генерация описания
  const getDescription = () => {
    if (description) return description;
    if (isCreateMode) {
      return `Создание новой записи в коллекции ${collection}`;
    }
    if (showQuickFields) {
      return 'Редактирование основных полей записи. Для полного редактирования используйте PocketBase Admin.';
    }
    return `Редактирование записи в коллекции ${collection}`;
  };

  // Рендер содержимого формы
  const renderFormContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-[#8BC34A]" />
            <p className="text-gray-600">Загрузка данных...</p>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-8">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadRecord} variant="outline">
            Попробовать снова
          </Button>
        </div>
      );
    }

    if (schema.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-600">Нет доступных полей для редактирования</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {schema.map((field) => (
          <FieldRenderer
            key={field.name}
            field={field}
            value={formData[field.name]}
            onChange={(value) => handleFieldChange(field.name, value)}
            relationOptions={relationData[field.name] || []}
            pbUrl={pbUrl}
            collection={collection}
            recordId={recordId}
            disabled={saving}
          />
        ))}
      </div>
    );
  };

  // Рендер кнопок действий
  const renderActions = () => (
    <div className="flex items-center justify-between pt-6 border-t">
      <div className="text-xs text-gray-500">
        {isCreateMode ? 'Новая запись' : `ID: ${recordId}`}
        {success && (
          <span className="ml-4 text-green-600">✓ {isCreateMode ? 'Создано' : 'Сохранено'}</span>
        )}
      </div>
      <div className="flex gap-2">
        <Button variant="outline" onClick={handleClose} disabled={saving}>
          <X className="w-4 h-4 mr-1" />
          Отмена
        </Button>
        {!showQuickFields && (
          <Button
            variant="outline"
            onClick={() => window.open(`${pbUrl}/_/#/collections?collection=pbc_${collection}&recordId=${recordId}`, '_blank')}
            disabled={saving}
          >
            <ExternalLink className="w-4 h-4 mr-1" />
            PocketBase
          </Button>
        )}
        <Button
          onClick={handleSave}
          disabled={saving}
          className="bg-[#8BC34A] hover:bg-[#4E8C29]"
        >
          {saving ? (
            <Loader2 className="w-4 h-4 mr-1 animate-spin" />
          ) : (
            <Save className="w-4 h-4 mr-1" />
          )}
          {saving ? (isCreateMode ? 'Создание...' : 'Сохранение...') : (isCreateMode ? 'Создать' : 'Сохранить')}
        </Button>
      </div>
    </div>
  );

  // Рендер в режиме модального окна
  if (mode === 'modal') {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-6xl lg:max-w-7xl max-h-[95vh] overflow-y-auto w-[95vw]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{getTitle()}</span>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{collection}</Badge>
                {!showQuickFields && (
                  <Button
                    size="sm"
                    variant="outline"
                    // onClick={() => window.open(`${pbUrl}/_/#/collections?collection=pbc_${collection}&recordId=${recordId}`, '_blank')}
                    onClick={() => window.open(`${pbUrl}/_/#/collections?collection=pbc_${collection}&recordId=${recordId}`, '_blank')}
    
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </DialogTitle>
            <DialogDescription>
              {getDescription()}
            </DialogDescription>
          </DialogHeader>

          <div className="mt-6">
            {renderFormContent()}
            {!loading && !error && renderActions()}
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Рендер в inline режиме
  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h2 className="text-xl font-semibold">{getTitle()}</h2>
        <p className="text-gray-600 text-sm mt-1">{getDescription()}</p>
      </div>

      {renderFormContent()}
      {!loading && !error && renderActions()}
    </div>
  );
};
