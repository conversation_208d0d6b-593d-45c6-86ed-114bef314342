---
import '../styles/global.css'
// Component Imports
import Layout from '../layouts/Layout.astro'
import { HomeSection } from '@/components/home'
import { getPromos, getHtmlBlocks, type Promo, type HtmlBlocks } from '@/lib/api'
import { getPageSEO } from '@/lib/seo-config'

// Получаем данные из PocketBase
let promos: Promo[] = [];
let htmlBlocks: HtmlBlocks[] = [];

// Получаем акции
try {
  const response = await getPromos();
  console.log('Получено акций из PocketBase на главной странице:', response?.length || 0);

  if (response && response.length > 0) {
    promos = response;
  } else {
    console.warn('Нет данных об акциях в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении акций:', error);
}

// Получаем HTML блоки
try {
  const response = await getHtmlBlocks();
  console.log('Получено HTML блоков из PocketBase на главной странице:', response?.length || 0);

  if (response && response.length > 0) {
    htmlBlocks = response;
  } else {
    console.warn('Нет данных о HTML блоках в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении HTML блоков:', error);
}

// Получаем SEO данные для главной страницы
const seo = getPageSEO('home');
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
>
  <HomeSection promos={promos} htmlBlocks={htmlBlocks} client:load/>
</Layout>