---
import Layout from '@/layouts/Layout.astro';
import { CollectionManager } from '@/components/admin/CollectionManager';
import { isUserAuthenticated } from '@/middleware/auth';

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Если не авторизован, перенаправляем на главную админ-страницу
if (!isAuthenticated) {
  return Astro.redirect('/admin');
}
---

<Layout 
  title="Управление специалистами - Админ-панель"
  description="Управление информацией о врачах и специалистах клиники"
  noindex={true}
>
  <div class="min-h-screen bg-gradient-to-br from-[#8BC34A]/10 via-white to-[#8BC34A]/5">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center gap-3">
            <a href="/admin" class="text-[#8BC34A] hover:text-[#4E8C29] transition-colors">
              ← Назад к панели
            </a>
            <span class="text-gray-300">|</span>
            <h1 class="text-xl font-bold text-gray-900">Управление специалистами</h1>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <CollectionManager
        collection="doctors"
        title="Специалисты"
        description="Управление информацией о врачах и специалистах клиники"
        pbUrl="https://pb.stom-line.ru"
        client:only="react"
      />
    </main>
  </div>
</Layout>
