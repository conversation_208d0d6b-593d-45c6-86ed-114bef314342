---
import Layout from '@/layouts/Layout.astro';
---

<Layout title="Тест авторизации">
  <div class="container mx-auto p-6 max-w-2xl">
    <h1 class="text-3xl font-bold mb-6">Тест авторизации</h1>
    
    <div class="space-y-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 class="text-xl font-semibold text-blue-800 mb-4">
          Тестирование middleware
        </h2>
        <p class="text-blue-700 mb-4">
          Эта страница позволяет протестировать работу middleware авторизации.
        </p>
        
        <div class="space-y-4">
          <div>
            <label for="test-token" class="block text-sm font-medium text-blue-800 mb-2">
              Тестовый токен:
            </label>
            <input 
              type="text" 
              id="test-token" 
              placeholder="Вставьте токен для тестирования"
              class="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div class="flex gap-3">
            <button 
              onclick="setTestToken()" 
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Установить токен
            </button>
            
            <button 
              onclick="clearToken()" 
              class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Очистить токен
            </button>
            
            <button 
              onclick="checkAuth()" 
              class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              Проверить авторизацию
            </button>
          </div>
        </div>
      </div>
      
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Текущее состояние:</h3>
        <div id="auth-status" class="text-gray-600">
          Загрузка...
        </div>
      </div>
      
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-yellow-800 mb-4">Инструкции:</h3>
        <ol class="list-decimal list-inside text-yellow-700 space-y-2">
          <li>Перейдите на страницу <a href="/admin/edit/pages/do0q1anuy7nzxdx" class="underline">редактирования</a></li>
          <li>Авторизуйтесь с помощью логина и пароля</li>
          <li>Откройте консоль разработчика (F12)</li>
          <li>Выполните: <code class="bg-yellow-100 px-1 rounded">localStorage.getItem('pb_token')</code></li>
          <li>Скопируйте полученный токен и вставьте в поле выше</li>
          <li>Нажмите "Установить токен" и затем "Проверить авторизацию"</li>
        </ol>
      </div>
    </div>
  </div>

  <script>
    function setTestToken() {
      const token = document.getElementById('test-token').value;
      if (token) {
        localStorage.setItem('pb_token', token);
        // Устанавливаем cookie для middleware
        document.cookie = `pb_token=${token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        updateAuthStatus();
        alert('Токен установлен! Обновите страницу для проверки middleware.');
      } else {
        alert('Введите токен');
      }
    }
    
    function clearToken() {
      localStorage.removeItem('pb_token');
      document.cookie = 'pb_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      updateAuthStatus();
      alert('Токен очищен! Обновите страницу для проверки middleware.');
    }
    
    function checkAuth() {
      window.location.href = '/admin/login';
    }
    
    function updateAuthStatus() {
      const token = localStorage.getItem('pb_token');
      const statusDiv = document.getElementById('auth-status');
      
      if (token) {
        statusDiv.innerHTML = `
          <div class="text-green-600">
            <strong>✅ Токен найден в localStorage</strong><br>
            <span class="text-sm">Токен: ***${token.slice(-10)}</span>
          </div>
        `;
      } else {
        statusDiv.innerHTML = `
          <div class="text-red-600">
            <strong>❌ Токен не найден в localStorage</strong>
          </div>
        `;
      }
    }
    
    // Обновляем статус при загрузке страницы
    document.addEventListener('DOMContentLoaded', updateAuthStatus);
  </script>
</Layout>
