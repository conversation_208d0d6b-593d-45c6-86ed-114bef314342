---
import Layout from '@/layouts/Layout.astro';
import { CollectionManager } from '@/components/admin/CollectionManager';
import { isUserAuthenticated } from '@/middleware/auth';

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Если не авторизован, перенаправляем на страницу входа
if (!isAuthenticated) {
  return Astro.redirect('/admin/login');
}
---

<Layout 
  title="Персонал - Панель администратора"
  description="Управление информацией о сотрудниках клиники"
  noindex={true}
>
  <div class="min-h-screen bg-gradient-to-br from-[#8BC34A]/10 via-white to-[#8BC34A]/5">
    <!-- Header -->
    <header class="border-b border-gray-200 bg-white shadow-sm">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center gap-3">
            <a href="/admin" class="flex items-center gap-3 text-gray-900 hover:text-[#8BC34A] transition-colors">
              <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-[#8BC34A] to-[#4E8C29]">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
              </div>
              <div>
                <h1 class="text-xl font-bold">Персонал</h1>
                <p class="text-sm text-gray-500">Управление сотрудниками</p>
              </div>
            </a>
          </div>
          <a 
            href="/admin" 
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            Назад к панели
          </a>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
      <CollectionManager
        collection="personal"
        title="Персонал"
        description="Сотрудники клиники (администраторы, медсестры, техперсонал)"
        pbUrl="https://pb.stom-line.ru"
        client:only="react"
      />
    </main>
  </div>
</Layout>
