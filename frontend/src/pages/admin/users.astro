---
import Layout from '@/layouts/Layout.astro';
import { CollectionManager } from '@/components/admin/CollectionManager';
import { isUserAuthenticated, isAdmin } from '@/middleware/auth';

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);
const hasAdminRights = isAdmin(Astro.locals);

// Если не авторизован или не админ, перенаправляем
if (!isAuthenticated || !hasAdminRights) {
  return Astro.redirect('/admin');
}
---

<Layout 
  title="Управление пользователями - Админ-панель"
  description="Управление пользователями сайта стоматологической клиники"
  noindex={true}
>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <a 
              href="/admin" 
              class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Назад к панели
            </a>
            <div class="h-4 w-px bg-gray-300"></div>
            <h1 class="text-xl font-semibold text-gray-900">Управление пользователями</h1>
          </div>
          
          <div class="flex items-center space-x-3">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              Только для администраторов
            </span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Warning Banner -->
      <div class="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-start">
          <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
          <div>
            <h3 class="text-sm font-medium text-yellow-800">Осторожно!</h3>
            <p class="mt-1 text-sm text-yellow-700">
              Управление пользователями требует особой осторожности. Изменение данных пользователей может повлиять на их доступ к сайту.
              Не удаляйте пользователей без крайней необходимости.
            </p>
          </div>
        </div>
      </div>

      <!-- Info Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Пользователи сайта</h3>
              <p class="text-sm text-gray-500">Обычные посетители с аккаунтами</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Верификация</h3>
              <p class="text-sm text-gray-500">Подтверждение email адресов</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Безопасность</h3>
              <p class="text-sm text-gray-500">Управление доступом и правами</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Collection Manager -->
      <CollectionManager
        collection="users"
        title="Пользователи сайта"
        description="Управление аккаунтами пользователей сайта. Здесь можно просматривать профили, управлять статусом верификации и при необходимости блокировать аккаунты."
        pbUrl="https://pb.stom-line.ru"
        client:only="react"
      />
    </main>
  </div>
</Layout>

<style>
  /* Дополнительные стили для управления пользователями */
  .user-verified {
    background-color: #e8f2e2; /* olive-100 */
    color: #2e531a; /* olive-800 */
  }

  .user-unverified {
    background-color: #fee2e2; /* red-100 */
    color: #991b1b; /* red-800 */
  }

  .status-deleted {
    background-color: #fee2e2; /* red-100 */
    color: #991b1b; /* red-800 */
  }
  
  .avatar-img {
    width: 2.5rem; /* w-10 */
    height: 2.5rem; /* h-10 */
    border-radius: 9999px; /* rounded-full */
    object-fit: cover;
  }
  
  .avatar-placeholder {
    width: 2.5rem; /* w-10 */
    height: 2.5rem; /* h-10 */
    border-radius: 9999px; /* rounded-full */
    background-color: #d1d5db; /* bg-gray-300 */
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4b5563; /* text-gray-600 */
    font-size: 0.875rem; /* text-sm */
    font-weight: 500; /* font-medium */
  }
</style>

<script>
  // Дополнительная безопасность - подтверждение критических действий
  if (typeof window !== 'undefined') {
    // Перехватываем клики по кнопкам удаления
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target.closest('[data-action="delete-user"]')) {
        const confirmed = confirm(
          'Вы уверены, что хотите удалить этого пользователя?\n\n' +
          'Это действие нельзя отменить и может повлиять на связанные данные.'
        );
        if (!confirmed) {
          e.preventDefault();
          e.stopPropagation();
        }
      }
    });
  }
</script>
