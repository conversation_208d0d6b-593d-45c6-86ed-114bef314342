import type { APIRoute } from 'astro';
import { initMeiliSearch, syncPocketBaseToMeiliSearch } from '@/lib/sync-meilisearch';

export const POST: APIRoute = async ({ request }) => {
  try {
    // Проверяем авторизацию по API-ключу
    const apiKey = request.headers.get('x-api-key');
    const expectedApiKey = import.meta.env.API_KEY || 'stomline-sync-key';
    
    if (apiKey !== expectedApiKey) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Unauthorized: Invalid API key'
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }
    
    // Получаем параметры из запроса
    const body = await request.json();
    const { action = 'sync' } = body;
    
    let result;
    
    // Выполняем соответствующее действие
    if (action === 'init') {
      // Полная инициализация (настройка индексов, синхронизация данных, создание общего индекса)
      result = await initMeiliSearch();
    } else {
      // Только синхронизация данных
      result = await syncPocketBaseToMeiliSearch();
    }
    
    return new Response(
      JSON.stringify({
        success: true,
        message: action === 'init' ? 'MeiliSearch initialized successfully' : 'Data synchronized successfully',
        result
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  } catch (error) {
    console.error('Error in sync-search API:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Error processing request',
        error: error instanceof Error ? error.message : String(error)
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
};
