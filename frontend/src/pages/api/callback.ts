import type { APIRoute } from 'astro'
import pb from '@/lib/pocketbase'
import type { CallbackFormData, CallbackApiResponse } from '@/lib/api'

// Валидация телефона (синхронизировано с фронтендом)
const validatePhone = (phone: string): boolean => {
  // Очищаем номер от всех символов кроме цифр и +
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '')

  // Проверяем российские номера: +7XXXXXXXXXX, 8XXXXXXXXXX или 7XXXXXXXXXX
  // где X - цифры, первая цифра после кода страны должна быть 4, 8 или 9
  const phoneRegex = /^(\+7|8|7)?[489][0-9]{9}$/

  return phoneRegex.test(cleanPhone)
}

// Валидация имени (синхронизировано с фронтендом)
const validateName = (name: string): boolean => {
  const trimmedName = name.trim()
  return trimmedName.length >= 2 &&
         trimmedName.length <= 50 &&
         /^[а-яёА-ЯЁa-zA-Z\s\-]+$/.test(trimmedName)
}

// Интерфейс для входящих данных
interface RequestData {
  name: string;
  phone: string;
  message?: string;
}

// Валидация данных формы с улучшенными сообщениями об ошибках
const validateFormData = (data: RequestData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Проверка имени с детальными сообщениями
  if (!data.name || typeof data.name !== 'string') {
    errors.push('Имя обязательно для заполнения')
  } else if (!validateName(data.name)) {
    const trimmedName = data.name.trim()
    if (trimmedName.length < 2) {
      errors.push('Имя должно содержать не менее 2 символов')
    } else if (trimmedName.length > 50) {
      errors.push('Имя не должно превышать 50 символов')
    } else {
      errors.push('Имя должно содержать только буквы, пробелы и дефисы')
    }
  }

  // Проверка телефона с примером формата
  if (!data.phone || typeof data.phone !== 'string') {
    errors.push('Телефон обязателен для заполнения')
  } else if (!validatePhone(data.phone)) {
    errors.push('Введите корректный российский номер телефона (например: ****** 123-45-67)')
  }

  // Проверка сообщения - увеличиваем лимит до 1000 символов для соответствия фронтенду
  if (data.message && typeof data.message === 'string' && data.message.length > 1000) {
    errors.push('Сообщение не должно превышать 1000 символов')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Получение IP-адреса клиента
const getClientIP = (request: Request): string => {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }

  return realIP || remoteAddr || 'unknown'
}

// Определение источника запроса
const getRequestSource = (request: Request): string => {
  const referer = request.headers.get('referer')

  if (!referer) return 'direct'

  try {
    const url = new URL(referer)
    const pathname = url.pathname

    // Определяем источник по URL
    if (pathname === '/') return 'homepage-form'
    if (pathname.startsWith('/services')) return 'services-page-form'
    if (pathname.startsWith('/specialists')) return 'specialists-page-form'
    if (pathname.startsWith('/contacts')) return 'contacts-page-form'
    if (pathname.startsWith('/promos')) return 'promo-page-form'

    return `${pathname}-form`
  } catch {
    return 'unknown-page'
  }
}

// Сохранение заявки в PocketBase (с заглушкой для тестирования)
const saveCallbackRequest = async (
  data: CallbackFormData,
  request: Request
): Promise<{ success: boolean; id?: string; error?: string }> => {
  try {
    // Подготавливаем данные для сохранения в соответствии со схемой PocketBase
    const recordData = {
      name: data.name.trim(),
      phone: data.phone.trim(),
      message: data.message?.trim() || '',
      ip_address: getClientIP(request),
      isProcessed: false // По умолчанию заявка не обработана
    }

    // Проверяем, находимся ли мы в режиме разработки
    const isDevelopment = import.meta.env.DEV || process.env.NODE_ENV === 'development'

    if (isDevelopment) {
      // В режиме разработки используем заглушку
      console.log('РЕЖИМ РАЗРАБОТКИ: Заявка на обратный звонок (заглушка):', {
        id: 'dev_' + Date.now(),
        name: recordData.name,
        phone: recordData.phone,
        message: recordData.message,
        ip_address: recordData.ip_address,
        timestamp: new Date().toISOString()
      })

      return {
        success: true,
        id: 'dev_' + Date.now()
      }
    }

    // В продакшене пытаемся сохранить в PocketBase
    const record = await pb.collection('callback_requests').create(recordData)

    console.log('Заявка на обратный звонок сохранена:', {
      id: record.id,
      name: recordData.name,
      phone: recordData.phone,
      ip_address: recordData.ip_address,
      timestamp: record.created
    })

    return {
      success: true,
      id: record.id
    }
  } catch (error: any) {
    console.error('Ошибка при сохранении заявки в PocketBase:', error)

    // Обработка специфичных ошибок PocketBase
    if (error.response?.data) {
      const pbError = error.response.data
      return {
        success: false,
        error: `Ошибка валидации: ${JSON.stringify(pbError)}`
      }
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Неизвестная ошибка при сохранении'
    }
  }
}

// Отправка уведомления (заглушка)
const sendNotification = async (data: CallbackFormData, requestId: string): Promise<void> => {
  try {
    // Здесь будет логика отправки уведомлений:
    // - Email администратору
    // - SMS уведомление
    // - Webhook в CRM систему
    // - Уведомление в Telegram/Slack
    
    console.log('Отправка уведомления о новой заявке:', {
      requestId,
      name: data.name,
      phone: data.phone,
      message: data.message
    })

    // Симуляция отправки уведомления
    await new Promise(resolve => setTimeout(resolve, 500))
    
  } catch (error) {
    console.error('Ошибка при отправке уведомления:', error)
    // Не прерываем выполнение, если уведомление не отправилось
  }
}

export const POST: APIRoute = async ({ request }) => {
  try {
    // Проверяем Content-Type
    const contentType = request.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Неверный Content-Type. Ожидается application/json'
        } as CallbackApiResponse),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
    }

    // Парсим данные из запроса
    let requestData: RequestData
    try {
      requestData = await request.json()
    } catch {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Неверный формат JSON'
        } as CallbackApiResponse),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
    }

    // Валидируем данные
    const validation = validateFormData(requestData)
    if (!validation.isValid) {
      return new Response(
        JSON.stringify({
          success: false,
          message: `Ошибки валидации: ${validation.errors.join(', ')}`
        } as CallbackApiResponse),
        {
          status: 422,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
    }

    // Подготавливаем данные для сохранения
    const formData: CallbackFormData = {
      name: requestData.name.trim(),
      phone: requestData.phone.trim(),
      message: requestData.message?.trim() || ''
    }

    // Сохраняем заявку
    const saveResult = await saveCallbackRequest(formData, request)
    
    if (!saveResult.success) {
      return new Response(
        JSON.stringify({
          success: false,
          message: saveResult.error || 'Ошибка при сохранении заявки'
        } as CallbackApiResponse),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
    }

    // Отправляем уведомления (асинхронно, не ждем результата)
    if (saveResult.id) {
      sendNotification(formData, saveResult.id).catch(console.error)
    }

    // Возвращаем успешный ответ
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Заявка успешно отправлена! Мы свяжемся с вами в ближайшее время.',
        id: saveResult.id
      } as CallbackApiResponse),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )

  } catch (error) {
    console.error('Неожиданная ошибка в API callback:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Внутренняя ошибка сервера'
      } as CallbackApiResponse),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}

// Обработка других HTTP методов
export const GET: APIRoute = async () => {
  return new Response(
    JSON.stringify({
      success: false,
      message: 'Метод GET не поддерживается. Используйте POST для отправки заявки.'
    } as CallbackApiResponse),
    {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
        'Allow': 'POST'
      }
    }
  )
}
