---
import Layout from '@/layouts/Layout.astro'
import { FAQPage } from '@/components/faq-page'
import { getFAQs, type FAQ } from '@/lib/api'
import { getPageSEO, generateFAQJsonLd } from '@/lib/seo-config'
import RDFaMarkup from '@/components/RDFaMarkup.astro'
import { isUserAuthenticated } from '@/middleware/auth'

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Получаем SEO данные для страницы FAQ
const seo = getPageSEO('faq');

// Получаем данные из PocketBase
let faqs: FAQ[] = [];

try {
  const response = await getFAQs();
  console.log('Получено FAQ из PocketBase на странице FAQ:', response?.length || 0);

  if (response && response.length > 0) {
    faqs = response;
  } else {
    console.warn('Нет данных о FAQ в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении FAQ:', error);
}

// Генерируем структурированные данные для FAQ
const faqJsonLd = faqs.length > 0 ? generateFAQJsonLd(faqs) : null;
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
  jsonLd={faqJsonLd}
>
  <FAQPage faqs={faqs} isAuthenticated={isAuthenticated} client:load />

  <!-- RDFa разметка для FAQ -->
  <RDFaMarkup type="faq" data={faqs} />
</Layout>
