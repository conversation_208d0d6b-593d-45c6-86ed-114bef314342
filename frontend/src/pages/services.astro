---
import Layout from '@/layouts/Layout.astro'
import { ServicesPageNew } from '@/components/ServicesPageNew'
import { getServices, getServiceCategories, type Service, type ServiceCategory } from '@/lib/api'
import { getPageSEO } from '@/lib/seo-config'
import { isUserAuthenticated } from '@/middleware/auth'

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Получаем параметры из URL
const specialist = Astro.url.searchParams.get('specialist');

// Получаем SEO данные для страницы услуг
const seo = getPageSEO('services');

// Формируем заголовок страницы
let title = seo.title;
let description = seo.description;

if (specialist) {
  title = `Услуги специалиста - ${seo.title}`;
  description = `Услуги выбранного специалиста в стоматологической клинике STOM-LINE в Мурманске.`;
}

// Получаем данные из PocketBase
let services: Service[] = [];
let categories: ServiceCategory[] = [];
let filter = '';

// Если указан специалист, фильтруем услуги по нему (пока закомментируем, так как связи с врачами могут отличаться)
// if (specialist) {
//   filter = `doctors.id = "${specialist}"`;
// }

try {
  const [servicesResponse, categoriesResponse] = await Promise.all([
    getServices(filter),
    getServiceCategories()
  ]);
  
  console.log('Получено услуг из PocketBase:', servicesResponse?.length || 0);
  console.log('Получено категорий из PocketBase:', categoriesResponse?.length || 0);

  if (servicesResponse && servicesResponse.length > 0) {
    services = servicesResponse;
  } else {
    console.warn('Нет данных об услугах в PocketBase');
  }

  if (categoriesResponse && categoriesResponse.length > 0) {
    categories = categoriesResponse;
  }
} catch (error) {
  console.error('Ошибка при получении данных:', error);
}
---

<Layout
  title={title}
  description={description}
  keywords={seo.keywords}
  type={seo.type}
>
  <ServicesPageNew
    specialist={specialist ? specialist.toString() : undefined}
    services={services}
    isAuthenticated={isAuthenticated}
    client:load
  />
</Layout>
