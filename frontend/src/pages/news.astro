---
import Layout from '@/layouts/Layout.astro'
import { NewsPage } from '@/components/news-page'
import { getNews, type News } from '@/lib/api'
import { getPageSEO } from '@/lib/seo-config'
import { isUserAuthenticated } from '@/middleware/auth'

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Получаем SEO данные для страницы новостей
const seo = getPageSEO('news');

// Получаем данные из PocketBase
let news: News[] = [];

try {
  const response = await getNews();
  console.log('Получено новостей из PocketBase на странице новостей:', response?.length || 0);

  if (response && response.length > 0) {
    news = response;
  } else {
    console.warn('Нет данных о новостях в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении новостей:', error);
}
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
>
  <NewsPage news={news} isAuthenticated={isAuthenticated} client:load />
</Layout>
