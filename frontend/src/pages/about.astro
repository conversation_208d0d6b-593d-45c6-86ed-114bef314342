---
import Layout from '@/layouts/Layout.astro'
import AchievementsSection from '@/components/achievements-section'
import { getHtmlBlocks, type HtmlBlocks } from '@/lib/api'
import { getPageSEO } from '@/lib/seo-config'
import { isUserAuthenticated } from '@/middleware/auth'

// Получаем SEO данные для страницы "О нас"
const seo = getPageSEO('about');

// Проверяем авторизацию пользователя
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Получаем HTML блоки из PocketBase
let htmlBlocks: HtmlBlocks[] = [];

try {
  const response = await getHtmlBlocks();
  if (response && response.length > 0) {
    htmlBlocks = response;
  }
} catch (error) {
  console.error('Ошибка при получении HTML блоков:', error);
}
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
>
  <main>
    <AchievementsSection htmlBlocks={htmlBlocks} isAuthenticated={isAuthenticated} client:load />
  </main>
</Layout>
