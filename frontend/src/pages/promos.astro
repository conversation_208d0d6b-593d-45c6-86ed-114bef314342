---
import Layout from '@/layouts/Layout.astro'
import { PromosPage } from '@/components/promos-page'
import { getPromos, type Promo } from '@/lib/api'
import { getPageSEO } from '@/lib/seo-config'

// Получаем SEO данные для страницы акций
const seo = getPageSEO('promos');

// Получаем данные из PocketBase
let promos: Promo[] = [];

try {
  // Получаем все акции без фильтра по активности
  const response = await getPromos('');
  console.log('Получено акций из PocketBase на странице акций:', response?.length || 0);

  if (response && response.length > 0) {
    promos = response;
  } else {
    console.warn('Нет данных об акциях в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении акций:', error);
}
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
>
  <PromosPage promos={promos} client:load />
</Layout>
