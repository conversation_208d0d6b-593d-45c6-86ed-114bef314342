// SEO валидатор для проверки корректности мета-тегов и структурированных данных

export interface SEOValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number;
}

export interface PageSEOData {
  title?: string;
  description?: string;
  keywords?: string[];
  url?: string;
  image?: string;
  jsonLd?: any;
}

export class SEOValidator {
  
  static validateTitle(title: string): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!title) {
      errors.push('Title отсутствует');
      return { isValid: false, errors, warnings };
    }
    
    if (title.length < 30) {
      warnings.push('Title слишком короткий (рекомендуется 30-60 символов)');
    }
    
    if (title.length > 60) {
      warnings.push('Title слишком длинный (рекомендуется 30-60 символов)');
    }
    
    if (!title.includes('STOM-LINE') && !title.includes('Мурманск')) {
      warnings.push('Title не содержит название бренда или города');
    }
    
    return { isValid: true, errors, warnings };
  }
  
  static validateDescription(description: string): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!description) {
      errors.push('Description отсутствует');
      return { isValid: false, errors, warnings };
    }
    
    if (description.length < 120) {
      warnings.push('Description слишком короткий (рекомендуется 120-160 символов)');
    }
    
    if (description.length > 160) {
      warnings.push('Description слишком длинный (рекомендуется 120-160 символов)');
    }
    
    if (!description.includes('стоматолог') && !description.includes('зуб')) {
      warnings.push('Description не содержит ключевые слова стоматологии');
    }
    
    return { isValid: true, errors, warnings };
  }
  
  static validateKeywords(keywords: string[]): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!keywords || keywords.length === 0) {
      warnings.push('Keywords отсутствуют');
      return { isValid: true, errors, warnings };
    }
    
    if (keywords.length > 10) {
      warnings.push('Слишком много keywords (рекомендуется до 10)');
    }
    
    const hasLocationKeyword = keywords.some(k => k.toLowerCase().includes('мурманск'));
    if (!hasLocationKeyword) {
      warnings.push('Keywords не содержат географическую привязку');
    }
    
    return { isValid: true, errors, warnings };
  }
  
  static validateImage(imageUrl: string): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!imageUrl) {
      errors.push('Open Graph изображение отсутствует');
      return { isValid: false, errors, warnings };
    }
    
    if (!imageUrl.startsWith('http')) {
      warnings.push('Изображение должно иметь абсолютный URL');
    }
    
    if (!imageUrl.includes('1200x630') && !imageUrl.includes('og-image')) {
      warnings.push('Рекомендуется использовать изображение размером 1200x630px');
    }
    
    return { isValid: true, errors, warnings };
  }
  
  static validateJsonLd(jsonLd: any): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!jsonLd) {
      warnings.push('Структурированные данные JSON-LD отсутствуют');
      return { isValid: true, errors, warnings };
    }
    
    try {
      const jsonString = typeof jsonLd === 'string' ? jsonLd : JSON.stringify(jsonLd);
      const parsed = JSON.parse(jsonString);
      
      if (!parsed['@context']) {
        errors.push('JSON-LD: отсутствует @context');
      }
      
      if (!parsed['@type']) {
        errors.push('JSON-LD: отсутствует @type');
      }
      
      if (parsed['@type'] === 'Organization' || parsed['@type'] === 'Dentist') {
        if (!parsed.name) {
          errors.push('JSON-LD: отсутствует name для организации');
        }
        if (!parsed.address) {
          warnings.push('JSON-LD: отсутствует address для организации');
        }
        if (!parsed.telephone) {
          warnings.push('JSON-LD: отсутствует telephone для организации');
        }
      }
      
      if (parsed['@type'] === 'Person') {
        if (!parsed.name) {
          errors.push('JSON-LD: отсутствует name для персоны');
        }
        if (!parsed.jobTitle) {
          warnings.push('JSON-LD: отсутствует jobTitle для персоны');
        }
      }
      
    } catch (e) {
      errors.push('JSON-LD: некорректный JSON формат');
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }
  
  static validatePage(data: PageSEOData): SEOValidationResult {
    const allErrors: string[] = [];
    const allWarnings: string[] = [];
    
    // Валидация title
    const titleResult = this.validateTitle(data.title || '');
    allErrors.push(...titleResult.errors);
    allWarnings.push(...titleResult.warnings);
    
    // Валидация description
    const descResult = this.validateDescription(data.description || '');
    allErrors.push(...descResult.errors);
    allWarnings.push(...descResult.warnings);
    
    // Валидация keywords
    const keywordsResult = this.validateKeywords(data.keywords || []);
    allErrors.push(...keywordsResult.errors);
    allWarnings.push(...keywordsResult.warnings);
    
    // Валидация изображения
    const imageResult = this.validateImage(data.image || '');
    allErrors.push(...imageResult.errors);
    allWarnings.push(...imageResult.warnings);
    
    // Валидация JSON-LD
    const jsonLdResult = this.validateJsonLd(data.jsonLd);
    allErrors.push(...jsonLdResult.errors);
    allWarnings.push(...jsonLdResult.warnings);
    
    // Подсчет SEO score
    let score = 100;
    score -= allErrors.length * 20; // Критические ошибки
    score -= allWarnings.length * 5; // Предупреждения
    score = Math.max(0, score);
    
    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
      score
    };
  }
  
  static generateSEOReport(pages: { [key: string]: PageSEOData }): string {
    let report = '# SEO Отчет для STOM-LINE\n\n';
    
    let totalScore = 0;
    let pageCount = 0;
    
    for (const [pageName, pageData] of Object.entries(pages)) {
      const result = this.validatePage(pageData);
      totalScore += result.score;
      pageCount++;
      
      report += `## ${pageName}\n`;
      report += `**Оценка SEO:** ${result.score}/100\n`;
      report += `**Статус:** ${result.isValid ? '✅ Валидно' : '❌ Есть ошибки'}\n\n`;
      
      if (result.errors.length > 0) {
        report += '### Критические ошибки:\n';
        result.errors.forEach(error => {
          report += `- ❌ ${error}\n`;
        });
        report += '\n';
      }
      
      if (result.warnings.length > 0) {
        report += '### Предупреждения:\n';
        result.warnings.forEach(warning => {
          report += `- ⚠️ ${warning}\n`;
        });
        report += '\n';
      }
      
      report += '---\n\n';
    }
    
    const averageScore = Math.round(totalScore / pageCount);
    report += `## Общая оценка\n`;
    report += `**Средняя оценка SEO:** ${averageScore}/100\n`;
    report += `**Проверено страниц:** ${pageCount}\n\n`;
    
    if (averageScore >= 90) {
      report += '🎉 Отличная SEO оптимизация!\n';
    } else if (averageScore >= 70) {
      report += '👍 Хорошая SEO оптимизация, есть место для улучшений.\n';
    } else {
      report += '⚠️ SEO оптимизация требует доработки.\n';
    }
    
    return report;
  }
}
