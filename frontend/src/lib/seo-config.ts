// SEO конфигурация для стоматологической клиники STOM-LINE
export interface SEOConfig {
  site: {
    name: string;
    url: string;
    description: string;
    keywords: string[];
    author: string;
    language: string;
    locale: string;
  };
  organization: {
    name: string;
    legalName: string;
    description: string;
    url: string;
    logo: string;
    image: string;
    telephone: string;
    email: string;
    address: {
      streetAddress: string;
      addressLocality: string;
      addressRegion: string;
      postalCode: string;
      addressCountry: string;
    };
    geo: {
      latitude: number;
      longitude: number;
    };
    openingHours: string[];
    priceRange: string;
    paymentAccepted: string[];
    currenciesAccepted: string[];
    areaServed: string[];
    medicalSpecialty: string[];
  };
  social: {
    vk?: string;
    telegram?: string;
    whatsapp?: string;
    instagram?: string;
    youtube?: string;
  };
  defaults: {
    title: string;
    description: string;
    image: string;
    type: string;
  };
  pages: {
    [key: string]: {
      title: string;
      description: string;
      keywords?: string[];
      type?: string;
    };
  };
}

export const seoConfig: SEOConfig = {
  site: {
    name: 'STOM-LINE',
    url: 'https://stom-line.ru',
    description: 'Стоматологическая клиника STOM-LINE в Мурманске - современное оборудование, профессиональные врачи и комфортное лечение зубов',
    keywords: [
      'стоматология',
      'стоматологическая клиника',
      'лечение зубов',
      'Мурманск',
      'стоматолог',
      'имплантация',
      'протезирование',
      'отбеливание зубов',
      'детская стоматология',
      'ортодонтия',
      'брекеты',
      'виниры'
    ],
    author: 'STOM-LINE',
    language: 'ru',
    locale: 'ru_RU'
  },
  organization: {
    name: 'STOM-LINE',
    legalName: 'Стоматологическая клиника STOM-LINE',
    description: 'Современная стоматологическая клиника в Мурманске с опытными врачами и новейшим оборудованием',
    url: 'https://stom-line.ru',
    logo: 'https://stom-line.ru/logo.svg',
    image: 'https://stom-line.ru/og-image.jpg',
    telephone: '+7 (8152) 52-57-08',
    email: '<EMAIL>',
    address: {
      streetAddress: 'ул. Полярные Зори, д. 35/2',
      addressLocality: 'Мурманск',
      addressRegion: 'Мурманская область',
      postalCode: '183038',
      addressCountry: 'RU'
    },
    geo: {
      latitude: 68.9585,
      longitude: 33.0827
    },
    openingHours: [
      'Mo-Fr 09:00-20:00',
      'Sa 09:00-18:00',
      'Su closed'
    ],
    priceRange: '₽₽',
    paymentAccepted: ['Cash', 'CreditCard', 'BankTransfer'],
    currenciesAccepted: ['RUB'],
    areaServed: ['Мурманск', 'Мурманская область'],
    medicalSpecialty: [
      'Терапевтическая стоматология',
      'Хирургическая стоматология',
      'Ортопедическая стоматология',
      'Ортодонтия',
      'Детская стоматология',
      'Пародонтология',
      'Эндодонтия',
      'Имплантология'
    ]
  },
  social: {
    vk: 'https://vk.com/stomline',
    telegram: 'https://t.me/stomline',
    whatsapp: 'https://wa.me/***********',
    instagram: 'https://instagram.com/stomline'
  },
  defaults: {
    title: 'STOM-LINE - Стоматологическая клиника в Мурманске',
    description: 'Стоматологическая клиника STOM-LINE в Мурманске - современное оборудование, профессиональные врачи и комфортное лечение зубов. Записаться на прием: +7 (8152) 52-57-08',
    image: 'https://stom-line.ru/og-image.jpg',
    type: 'website'
  },
  pages: {
    home: {
      title: 'STOM-LINE - Стоматологическая клиника в Мурманске',
      description: 'Стоматологическая клиника STOM-LINE в Мурманске - современное оборудование, профессиональные врачи и комфортное лечение зубов. Записаться на прием: +7 (8152) 52-57-08',
      keywords: ['стоматология Мурманск', 'стоматологическая клиника', 'лечение зубов', 'стоматолог Мурманск'],
      type: 'website'
    },
    services: {
      title: 'Услуги стоматологии STOM-LINE в Мурманске - Цены и описание',
      description: 'Полный спектр стоматологических услуг в клинике STOM-LINE: лечение, протезирование, имплантация, отбеливание зубов. Современное оборудование и опытные врачи.',
      keywords: ['стоматологические услуги', 'лечение зубов Мурманск', 'имплантация зубов', 'протезирование зубов'],
      type: 'website'
    },
    specialists: {
      title: 'Врачи стоматологи STOM-LINE в Мурманске - Опытные специалисты',
      description: 'Команда профессиональных стоматологов клиники STOM-LINE в Мурманске. Опытные врачи с многолетним стажем и современными методиками лечения.',
      keywords: ['стоматологи Мурманск', 'врачи стоматологи', 'специалисты стоматологии'],
      type: 'website'
    },
    prices: {
      title: 'Цены на стоматологические услуги в клинике STOM-LINE Мурманск',
      description: 'Актуальные цены на все виды стоматологических услуг в клинике STOM-LINE. Доступные цены на лечение, протезирование и имплантацию зубов в Мурманске.',
      keywords: ['цены стоматология Мурманск', 'стоимость лечения зубов', 'цены на имплантацию'],
      type: 'website'
    },
    reviews: {
      title: 'Отзывы пациентов о стоматологии STOM-LINE в Мурманске',
      description: 'Реальные отзывы пациентов о качестве лечения в стоматологической клинике STOM-LINE. Читайте мнения о наших врачах и услугах.',
      keywords: ['отзывы стоматология Мурманск', 'отзывы о стоматологах', 'мнения пациентов'],
      type: 'website'
    },
    news: {
      title: 'Новости стоматологии STOM-LINE - Акции и события клиники',
      description: 'Последние новости, акции и события стоматологической клиники STOM-LINE в Мурманске. Будьте в курсе наших предложений и новинок.',
      keywords: ['новости стоматологии', 'акции стоматология Мурманск', 'события клиники'],
      type: 'website'
    },
    promos: {
      title: 'Акции и скидки в стоматологии STOM-LINE Мурманск',
      description: 'Выгодные акции и специальные предложения на стоматологические услуги в клинике STOM-LINE. Экономьте на лечении зубов в Мурманске.',
      keywords: ['акции стоматология', 'скидки на лечение зубов', 'специальные предложения Мурманск'],
      type: 'website'
    },
    faq: {
      title: 'Часто задаваемые вопросы - Стоматология STOM-LINE Мурманск',
      description: 'Ответы на популярные вопросы о стоматологическом лечении, процедурах и услугах клиники STOM-LINE в Мурманске.',
      keywords: ['вопросы о стоматологии', 'FAQ стоматология', 'ответы стоматолога'],
      type: 'website'
    },
    about: {
      title: 'О клинике STOM-LINE - Современная стоматология в Мурманске',
      description: 'Стоматологическая клиника STOM-LINE - современное оборудование, опытные врачи и индивидуальный подход к каждому пациенту в Мурманске.',
      keywords: ['о клинике', 'стоматология Мурманск', 'современная стоматология'],
      type: 'website'
    },
    documents: {
      title: 'Документы и лицензии стоматологии STOM-LINE - Официальные разрешения',
      description: 'Официальные документы, лицензии и сертификаты стоматологической клиники STOM-LINE. Все разрешения на медицинскую деятельность в соответствии с российским законодательством.',
      keywords: ['лицензия стоматологии', 'документы медицинской организации', 'сертификаты качества', 'разрешения на медицинскую деятельность'],
      type: 'website'
    }
  }
};

// Функция для получения SEO данных страницы
export function getPageSEO(pageKey: string, customTitle?: string, customDescription?: string) {
  const page = seoConfig.pages[pageKey] || seoConfig.pages.home;

  return {
    title: customTitle || page.title,
    description: customDescription || page.description,
    keywords: page.keywords || seoConfig.site.keywords,
    type: page.type || seoConfig.defaults.type,
    image: seoConfig.defaults.image,
    url: seoConfig.site.url
  };
}

// Функция для генерации JSON-LD структурированных данных организации
export function generateOrganizationJsonLd() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Dentist',
    name: seoConfig.organization.name,
    legalName: seoConfig.organization.legalName,
    description: seoConfig.organization.description,
    url: seoConfig.organization.url,
    logo: seoConfig.organization.logo,
    image: seoConfig.organization.image,
    telephone: seoConfig.organization.telephone,
    email: seoConfig.organization.email,
    address: {
      '@type': 'PostalAddress',
      streetAddress: seoConfig.organization.address.streetAddress,
      addressLocality: seoConfig.organization.address.addressLocality,
      addressRegion: seoConfig.organization.address.addressRegion,
      postalCode: seoConfig.organization.address.postalCode,
      addressCountry: seoConfig.organization.address.addressCountry
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: seoConfig.organization.geo.latitude,
      longitude: seoConfig.organization.geo.longitude
    },
    openingHours: seoConfig.organization.openingHours,
    priceRange: seoConfig.organization.priceRange,
    paymentAccepted: seoConfig.organization.paymentAccepted,
    currenciesAccepted: seoConfig.organization.currenciesAccepted,
    areaServed: seoConfig.organization.areaServed.map(area => ({
      '@type': 'City',
      name: area
    })),
    medicalSpecialty: seoConfig.organization.medicalSpecialty,
    sameAs: Object.values(seoConfig.social).filter(Boolean)
  };
}

// Функция для генерации JSON-LD для услуги
export function generateServiceJsonLd(service: any) {
  return {
    '@context': 'https://schema.org',
    '@type': 'MedicalProcedure',
    name: service.name,
    description: service.short_description || service.content,
    url: `${seoConfig.site.url}/services/${service.slug}`,
    provider: {
      '@type': 'Dentist',
      name: seoConfig.organization.name,
      url: seoConfig.organization.url
    },
    medicalSpecialty: service.expand?.category?.name || 'Стоматология'
  };
}

// Функция для генерации JSON-LD для врача
export function generateDoctorJsonLd(doctor: any) {
  const fullName = `${doctor.surname} ${doctor.name} ${doctor.patronymic || ''}`.trim();

  return {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: fullName,
    jobTitle: doctor.position,
    description: doctor.short_description,
    worksFor: {
      '@type': 'Dentist',
      name: seoConfig.organization.name,
      url: seoConfig.organization.url
    },
    url: `${seoConfig.site.url}/specialists/${doctor.slug}`,
    image: doctor.photo ? `${seoConfig.site.url}/api/files/doctors/${doctor.id}/${doctor.photo}` : undefined,
    medicalSpecialty: doctor.expand?.specializations?.map((spec: any) => spec.name) || []
  };
}

// Функция для генерации JSON-LD для новости/статьи
export function generateArticleJsonLd(article: any, type: 'news' | 'promo' = 'news') {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.meta_description || article.subtitle,
    url: `${seoConfig.site.url}/${type}/${article.slug}`,
    datePublished: article.date || article.created,
    dateModified: article.updated,
    author: {
      '@type': 'Organization',
      name: seoConfig.organization.name,
      url: seoConfig.organization.url
    },
    publisher: {
      '@type': 'Organization',
      name: seoConfig.organization.name,
      url: seoConfig.organization.url,
      logo: {
        '@type': 'ImageObject',
        url: seoConfig.organization.logo
      }
    },
    image: article.image ? `${seoConfig.site.url}/api/files/${type}/${article.id}/${article.image}` : seoConfig.defaults.image
  };
}

// Функция для генерации JSON-LD для FAQ
export function generateFAQJsonLd(faqs: any[]) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };
}
