import pb from './pocketbase';
import { MeiliSearch } from 'meilisearch';
import { INDEXES } from './meilisearch';

// Создаем экземпляр MeiliSearch с URL и API ключом из переменных окружения
const searchUrl = import.meta.env.PUBLIC_SEARCH_URL || 'https://search.stom-line.ru';
const searchApiKey = import.meta.env.PUBLIC_SEARCH_API_KEY || 'Nc040stomline';

// Проверяем, что URL имеет правильный формат
let baseUrl = searchUrl;
if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
  baseUrl = `https://${baseUrl}`;
}

// Создаем клиент MeiliSearch с мастер-ключом для записи
const searchClient = new MeiliSearch({
  host: baseUrl,
  apiKey: searchApiKey
});

/**
 * Синхронизация данных из PocketBase в MeiliSearch
 */
export async function syncPocketBaseToMeiliSearch() {
  try {
    console.log('Начинаем синхронизацию данных с MeiliSearch...');

    // Синхронизируем каждую коллекцию
    await Promise.all([
      syncDoctors(),
      syncServices(),
      syncFAQ(),
      syncNews(),
      syncPromos(),
      syncPrices(),
      syncPages()
    ]);

    console.log('Синхронизация с MeiliSearch успешно завершена!');
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации данных с MeiliSearch:', error);
    return false;
  }
}

/**
 * Синхронизация специалистов
 */
async function syncDoctors() {
  try {
    console.log('Начинаем синхронизацию специалистов...');

    // Получаем всех специалистов из PocketBase без фильтров
    const doctors = await pb.collection('doctors').getFullList();
    console.log(`Получено ${doctors.length} специалистов из PocketBase`);

    if (doctors.length === 0) {
      console.warn('Нет специалистов для синхронизации!');
      return false;
    }

    // Преобразуем данные для MeiliSearch
    const doctorsForMeiliSearch = doctors.map(doctor => {
      // Получаем URL фото, если есть
      let photoUrl = '';
      if (doctor.photo && doctor.photo.length > 0) {
        try {
          photoUrl = pb.files.getUrl(doctor, doctor.photo);
        } catch (e) {
          console.warn(`Не удалось получить URL фото для доктора ${doctor.id}:`, e);
        }
      }

      // Формируем полное имя для поиска
      const fullName = `${doctor.surname || ''} ${doctor.name || ''} ${doctor.patronymic || ''}`.trim();

      // Формируем список специализаций
      let specializations = [];
      if (doctor.expand?.specializations) {
        try {
          specializations = doctor.expand.specializations.map((spec: any) => spec.name);
        } catch (e) {
          console.warn(`Ошибка при обработке специализаций для доктора ${doctor.id}:`, e);
        }
      }

      // Формируем список услуг
      let services = [];
      if (doctor.expand?.services) {
        try {
          services = doctor.expand.services.map((service: any) => service.name);
        } catch (e) {
          console.warn(`Ошибка при обработке услуг для доктора ${doctor.id}:`, e);
        }
      }

      return {
        id: doctor.id,
        fullName,
        surname: doctor.surname || '',
        name: doctor.name || '',
        patronymic: doctor.patronymic || '',
        slug: doctor.slug || doctor.id,
        position: doctor.position || '',
        short_description: doctor.short_description || '',
        biography: doctor.biography || '',
        photo: photoUrl,
        specializations,
        services,
        experience: doctor.experience || '',
        clinic: doctor.clinic || '',
        is_featured: doctor.is_featured || false,
        _index: INDEXES.DOCTORS
      };
    });

    console.log(`Подготовлено ${doctorsForMeiliSearch.length} специалистов для MeiliSearch`);

    // Отправляем данные в MeiliSearch
    const index = searchClient.index(INDEXES.DOCTORS);
    await index.addDocuments(doctorsForMeiliSearch);

    console.log(`Синхронизировано ${doctorsForMeiliSearch.length} специалистов`);
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации специалистов:', error);
    console.error('Детали ошибки:', error.message, error.stack);
    return false;
  }
}

/**
 * Синхронизация услуг
 */
async function syncServices() {
  try {
    console.log('Начинаем синхронизацию услуг...');

    // Получаем все услуги из PocketBase без фильтров
    const services = await pb.collection('services').getFullList();
    console.log(`Получено ${services.length} услуг из PocketBase`);

    if (services.length === 0) {
      console.warn('Нет услуг для синхронизации!');
      return false;
    }

    // Преобразуем данные для MeiliSearch
    const servicesForMeiliSearch = services.map(service => {
      // Получаем URL изображения, если есть
      let imageUrl = '';
      if (service.image && service.image.length > 0) {
        try {
          imageUrl = pb.files.getUrl(service, service.image);
        } catch (e) {
          console.warn(`Не удалось получить URL изображения для услуги ${service.id}:`, e);
        }
      }

      // Получаем категорию
      let category = '';
      if (service.expand?.category) {
        try {
          category = service.expand.category.name;
        } catch (e) {
          console.warn(`Ошибка при обработке категории для услуги ${service.id}:`, e);
        }
      }

      return {
        id: service.id,
        name: service.name || '',
        slug: service.slug || service.id,
        category,
        category_id: service.category || '',
        short_description: service.short_description || '',
        content: service.content || '',
        image: imageUrl,
        meta_title: service.meta_title || '',
        meta_description: service.meta_description || '',
        is_featured: service.is_featured || false,
        _index: INDEXES.SERVICES
      };
    });

    console.log(`Подготовлено ${servicesForMeiliSearch.length} услуг для MeiliSearch`);

    // Отправляем данные в MeiliSearch
    const index = searchClient.index(INDEXES.SERVICES);
    await index.addDocuments(servicesForMeiliSearch);

    console.log(`Синхронизировано ${servicesForMeiliSearch.length} услуг`);
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации услуг:', error);
    console.error('Детали ошибки:', error.message, error.stack);
    return false;
  }
}

/**
 * Синхронизация FAQ
 */
async function syncFAQ() {
  try {
    // Получаем все FAQ из PocketBase
    const faqs = await pb.collection('faq').getFullList({
      filter: 'is_published = true'
    });

    // Преобразуем данные для MeiliSearch
    const faqsForMeiliSearch = faqs.map(faq => {
      return {
        id: faq.id,
        question: faq.question,
        answer: faq.answer || '',
        category: faq.category || '',
        _index: INDEXES.FAQ
      };
    });

    // Отправляем данные в MeiliSearch
    const index = searchClient.index(INDEXES.FAQ);
    await index.addDocuments(faqsForMeiliSearch);

    console.log(`Синхронизировано ${faqsForMeiliSearch.length} FAQ`);
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации FAQ:', error);
    return false;
  }
}

/**
 * Синхронизация новостей
 */
async function syncNews() {
  try {
    // Получаем все новости из PocketBase
    const news = await pb.collection('news').getFullList();

    // Преобразуем данные для MeiliSearch
    const newsForMeiliSearch = news.map(item => {
      // Получаем URL изображения, если есть
      let imageUrl = '';
      if (item.image && item.image.length > 0) {
        imageUrl = pb.files.getUrl(item, item.image);
      }

      return {
        id: item.id,
        title: item.title,
        slug: item.slug,
        date: item.date || '',
        content: item.content || '',
        image: imageUrl,
        meta_title: item.meta_title || '',
        meta_description: item.meta_description || '',
        is_featured: item.is_featured || false,
        _index: INDEXES.NEWS
      };
    });

    // Отправляем данные в MeiliSearch
    const index = searchClient.index(INDEXES.NEWS);
    await index.addDocuments(newsForMeiliSearch);

    console.log(`Синхронизировано ${newsForMeiliSearch.length} новостей`);
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации новостей:', error);
    return false;
  }
}

/**
 * Синхронизация акций
 */
async function syncPromos() {
  try {
    // Получаем все акции из PocketBase
    const promos = await pb.collection('promos').getFullList({
      filter: 'is_active = true',
      expand: 'related_services'
    });

    // Преобразуем данные для MeiliSearch
    const promosForMeiliSearch = promos.map(promo => {
      // Получаем URL изображения, если есть
      let imageUrl = '';
      if (promo.image && promo.image.length > 0) {
        imageUrl = pb.files.getUrl(promo, promo.image);
      }

      // Получаем связанные услуги
      let relatedServices = [];
      if (promo.expand?.related_services) {
        relatedServices = promo.expand.related_services.map((service: any) => service.name);
      }

      return {
        id: promo.id,
        title: promo.title,
        subtitle: promo.subtitle || '',
        slug: promo.slug,
        start_date: promo.start_date || '',
        end_date: promo.end_date || '',
        content: promo.content || '',
        image: imageUrl,
        related_services: relatedServices,
        meta_title: promo.meta_title || '',
        meta_description: promo.meta_description || '',
        is_active: promo.is_active || false,
        is_featured: promo.is_featured || false,
        _index: INDEXES.PROMOS
      };
    });

    // Отправляем данные в MeiliSearch
    const index = searchClient.index(INDEXES.PROMOS);
    await index.addDocuments(promosForMeiliSearch);

    console.log(`Синхронизировано ${promosForMeiliSearch.length} акций`);
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации акций:', error);
    return false;
  }
}

/**
 * Синхронизация цен
 */
async function syncPrices() {
  try {
    console.log('Начинаем синхронизацию цен...');

    // Получаем все цены из PocketBase без фильтров
    const prices = await pb.collection('prices').getFullList();
    console.log(`Получено ${prices.length} цен из PocketBase`);

    if (prices.length === 0) {
      console.warn('Нет цен для синхронизации!');
      return false;
    }

    // Преобразуем данные для MeiliSearch
    const pricesForMeiliSearch = prices.map(price => {
      // Получаем название услуги и категории, если они есть
      let serviceName = '';
      let categoryName = '';

      if (price.expand?.service) {
        try {
          serviceName = price.expand.service.name;
        } catch (e) {
          console.warn(`Ошибка при обработке услуги для цены ${price.id}:`, e);
        }
      }

      if (price.expand?.category) {
        try {
          categoryName = price.expand.category.name;
        } catch (e) {
          console.warn(`Ошибка при обработке категории для цены ${price.id}:`, e);
        }
      }

      return {
        id: price.id,
        name: price.name || serviceName || 'Услуга',
        price: price.price || 0,
        old_price: price.old_price || 0,
        description: price.description || '',
        service_id: price.service || '',
        service_name: serviceName,
        category_id: price.category || '',
        category_name: categoryName,
        is_featured: price.is_featured || false,
        _index: INDEXES.PRICES
      };
    });

    console.log(`Подготовлено ${pricesForMeiliSearch.length} цен для MeiliSearch`);

    // Отправляем данные в MeiliSearch
    const index = searchClient.index(INDEXES.PRICES);
    await index.addDocuments(pricesForMeiliSearch);

    console.log(`Синхронизировано ${pricesForMeiliSearch.length} цен`);
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации цен:', error);
    console.error('Детали ошибки:', error.message, error.stack);
    return false;
  }
}

/**
 * Синхронизация страниц
 */
async function syncPages() {
  try {
    // Получаем все страницы из PocketBase
    const pages = await pb.collection('pages').getFullList({
      filter: 'is_published = true'
    });

    // Преобразуем данные для MeiliSearch
    const pagesForMeiliSearch = pages.map(page => {
      // Получаем URL изображения, если есть
      let imageUrl = '';
      if (page.featured_image && page.featured_image.length > 0) {
        imageUrl = pb.files.getUrl(page, page.featured_image);
      }

      return {
        id: page.id,
        title: page.title,
        slug: page.slug,
        content: page.content || '',
        image: imageUrl,
        parent_slug: page.parent_slug || '',
        meta_title: page.meta_title || '',
        meta_description: page.meta_description || '',
        _index: INDEXES.PAGES
      };
    });

    // Отправляем данные в MeiliSearch
    const index = searchClient.index(INDEXES.PAGES);
    await index.addDocuments(pagesForMeiliSearch);

    console.log(`Синхронизировано ${pagesForMeiliSearch.length} страниц`);
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации страниц:', error);
    return false;
  }
}

/**
 * Настройка индексов MeiliSearch
 */
export async function setupMeiliSearchIndexes() {
  try {
    console.log('Настройка индексов MeiliSearch...');

    // Настройка индекса специалистов
    const doctorsIndex = searchClient.index(INDEXES.DOCTORS);
    await doctorsIndex.updateSettings({
      searchableAttributes: [
        'fullName',
        'surname',
        'name',
        'patronymic',
        'position',
        'short_description',
        'biography',
        'specializations',
        'services'
      ],
      displayedAttributes: [
        'id',
        'fullName',
        'surname',
        'name',
        'patronymic',
        'slug',
        'position',
        'short_description',
        'photo',
        'specializations',
        'services',
        'experience',
        'clinic',
        'is_featured',
        '_index'
      ],
      filterableAttributes: [
        'specializations',
        'services',
        'is_featured'
      ],
      sortableAttributes: [
        'surname',
        'name'
      ]
    });

    // Настройка индекса услуг
    const servicesIndex = searchClient.index(INDEXES.SERVICES);
    await servicesIndex.updateSettings({
      searchableAttributes: [
        'name',
        'category',
        'short_description',
        'content'
      ],
      displayedAttributes: [
        'id',
        'name',
        'slug',
        'category',
        'category_id',
        'short_description',
        'content',
        'image',
        'meta_title',
        'meta_description',
        'is_featured',
        '_index'
      ],
      filterableAttributes: [
        'category',
        'category_id',
        'is_featured'
      ],
      sortableAttributes: [
        'name'
      ]
    });

    // Настройка индекса FAQ
    const faqIndex = searchClient.index(INDEXES.FAQ);
    await faqIndex.updateSettings({
      searchableAttributes: [
        'question',
        'answer',
        'category'
      ],
      displayedAttributes: [
        'id',
        'question',
        'answer',
        'category',
        '_index'
      ],
      filterableAttributes: [
        'category'
      ]
    });

    // Настройка индекса новостей
    const newsIndex = searchClient.index(INDEXES.NEWS);
    await newsIndex.updateSettings({
      searchableAttributes: [
        'title',
        'content'
      ],
      displayedAttributes: [
        'id',
        'title',
        'slug',
        'date',
        'content',
        'image',
        'meta_title',
        'meta_description',
        'is_featured',
        '_index'
      ],
      filterableAttributes: [
        'is_featured'
      ],
      sortableAttributes: [
        'date'
      ]
    });

    // Настройка индекса акций
    const promosIndex = searchClient.index(INDEXES.PROMOS);
    await promosIndex.updateSettings({
      searchableAttributes: [
        'title',
        'subtitle',
        'content',
        'related_services'
      ],
      displayedAttributes: [
        'id',
        'title',
        'subtitle',
        'slug',
        'start_date',
        'end_date',
        'content',
        'image',
        'related_services',
        'meta_title',
        'meta_description',
        'is_active',
        'is_featured',
        '_index'
      ],
      filterableAttributes: [
        'is_active',
        'is_featured'
      ],
      sortableAttributes: [
        'start_date',
        'end_date'
      ]
    });

    // Настройка индекса цен
    const pricesIndex = searchClient.index(INDEXES.PRICES);
    await pricesIndex.updateSettings({
      searchableAttributes: [
        'name',
        'description',
        'service_name',
        'category_name'
      ],
      displayedAttributes: [
        'id',
        'name',
        'price',
        'old_price',
        'description',
        'service_id',
        'service_name',
        'category_id',
        'category_name',
        'is_featured',
        '_index'
      ],
      filterableAttributes: [
        'service_id',
        'category_id',
        'is_featured'
      ],
      sortableAttributes: [
        'price',
        'name'
      ]
    });

    // Настройка индекса страниц
    const pagesIndex = searchClient.index(INDEXES.PAGES);
    await pagesIndex.updateSettings({
      searchableAttributes: [
        'title',
        'content'
      ],
      displayedAttributes: [
        'id',
        'title',
        'slug',
        'content',
        'image',
        'parent_slug',
        'meta_title',
        'meta_description',
        '_index'
      ],
      filterableAttributes: [
        'parent_slug'
      ]
    });

    console.log('Настройка индексов MeiliSearch успешно завершена!');
    return true;
  } catch (error) {
    console.error('Ошибка при настройке индексов MeiliSearch:', error);
    return false;
  }
}

/**
 * Создание индекса для поиска по всем коллекциям
 */
export async function createAllIndex() {
  try {
    console.log('Создание индекса для поиска по всем коллекциям...');

    // Получаем данные из всех индексов
    const [doctors, services, faqs, news, promos, pages] = await Promise.all([
      searchClient.index(INDEXES.DOCTORS).getDocuments({ limit: 1000 }),
      searchClient.index(INDEXES.SERVICES).getDocuments({ limit: 1000 }),
      searchClient.index(INDEXES.FAQ).getDocuments({ limit: 1000 }),
      searchClient.index(INDEXES.NEWS).getDocuments({ limit: 1000 }),
      searchClient.index(INDEXES.PROMOS).getDocuments({ limit: 1000 }),
      searchClient.index(INDEXES.PAGES).getDocuments({ limit: 1000 })
    ]);

    // Объединяем все документы
    const allDocuments = [
      ...doctors.results,
      ...services.results,
      ...faqs.results,
      ...news.results,
      ...promos.results,
      ...pages.results
    ];

    // Создаем индекс "all" для поиска по всем коллекциям
    const allIndex = searchClient.index('all');
    await allIndex.addDocuments(allDocuments);

    // Настраиваем индекс "all"
    await allIndex.updateSettings({
      searchableAttributes: [
        'fullName',
        'surname',
        'name',
        'patronymic',
        'position',
        'short_description',
        'biography',
        'specializations',
        'services',
        'title',
        'subtitle',
        'question',
        'answer',
        'category',
        'content'
      ],
      displayedAttributes: [
        'id',
        'fullName',
        'surname',
        'name',
        'patronymic',
        'slug',
        'position',
        'short_description',
        'photo',
        'specializations',
        'services',
        'experience',
        'clinic',
        'title',
        'subtitle',
        'question',
        'answer',
        'category',
        'content',
        'image',
        'meta_title',
        'meta_description',
        'is_featured',
        'is_active',
        '_index'
      ],
      filterableAttributes: [
        'specializations',
        'services',
        'category',
        'is_featured',
        'is_active',
        '_index'
      ]
    });

    console.log(`Создан индекс "all" с ${allDocuments.length} документами`);
    return true;
  } catch (error) {
    console.error('Ошибка при создании индекса для поиска по всем коллекциям:', error);
    return false;
  }
}

/**
 * Полная инициализация MeiliSearch
 */
export async function initMeiliSearch() {
  try {
    // Настраиваем индексы
    await setupMeiliSearchIndexes();

    // Синхронизируем данные
    await syncPocketBaseToMeiliSearch();

    // Создаем индекс для поиска по всем коллекциям
    await createAllIndex();

    console.log('Инициализация MeiliSearch успешно завершена!');
    return true;
  } catch (error) {
    console.error('Ошибка при инициализации MeiliSearch:', error);
    return false;
  }
}
