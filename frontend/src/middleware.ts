import { defineMiddleware } from 'astro:middleware';
import PocketBase from 'pocketbase';

// Получаем URL PocketBase из переменных окружения
const baseUrl = import.meta.env.PUBLIC_API_URL || 'https://pb.stom-line.ru';

/**
 * Проверяет валидность токена PocketBase
 * @param token JWT токен
 * @returns Promise<boolean>
 */
async function validateToken(token: string): Promise<{ isValid: boolean; user?: any }> {
  try {
    const pb = new PocketBase(baseUrl);

    // Устанавливаем токен в authStore
    pb.authStore.save(token);

    // Проверяем валидность токена
    if (!pb.authStore.isValid) {
      console.log('Токен не валиден по authStore.isValid');
      return { isValid: false };
    }

    console.log('AuthStore после save:', {
      isValid: pb.authStore.isValid,
      token: pb.authStore.token ? 'есть' : 'нет',
      record: pb.authStore.record,
      model: pb.authStore.model
    });

    // Пытаемся сначала как суперпользователя, потом как обычного пользователя
    // Сначала пробуем суперпользователя
    try {
      console.log('Пытаемся проверить как суперпользователя');
      await pb.collection('_superusers').authRefresh();

      if (pb.authStore.isValid && pb.authStore.record) {
        console.log('Суперпользователь успешно проверен');
        return {
          isValid: true,
          user: {
            id: pb.authStore.record.id,
            email: pb.authStore.record.email,
            token: pb.authStore.token,
            type: 'admin'
          }
        };
      }
    } catch (adminError) {
      console.log('Не суперпользователь, пробуем обычного пользователя. Ошибка:', adminError.message);

      // Если не получилось как суперпользователь, пробуем как обычного пользователя
      try {
        console.log('Пытаемся проверить как обычного пользователя');
        await pb.collection('users').authRefresh();

        if (pb.authStore.isValid && pb.authStore.record) {
          console.log('Обычный пользователь успешно проверен');
          return {
            isValid: true,
            user: {
              id: pb.authStore.record.id,
              email: pb.authStore.record.email,
              token: pb.authStore.token,
              type: 'user'
            }
          };
        }
      } catch (userError) {
        console.error('Ошибка при проверке токена пользователя:', userError.message);
        return { isValid: false };
      }
    }

    return { isValid: false };
  } catch (error) {
    console.error('Ошибка при валидации токена:', error);
    return { isValid: false };
  }
}

/**
 * Извлекает токен из cookies или заголовков
 * @param request Request объект
 * @returns string | null
 */
function extractToken(request: Request): string | null {
  // Проверяем заголовок Authorization
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Проверяем cookies
  const cookieHeader = request.headers.get('Cookie');
  if (cookieHeader) {
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    return cookies['pb_token'] || null;
  }

  return null;
}

export const onRequest = defineMiddleware(async (context, next) => {
  const { request, locals } = context;

  // Инициализируем locals с значениями по умолчанию
  locals.isAuthenticated = false;
  locals.user = undefined;

  // Извлекаем токен из запроса
  const token = extractToken(request);
  console.log('Извлеченный токен:', token ? `${token.substring(0, 20)}...` : 'null');

  if (token) {
    // Всегда проверяем токен, независимо от режима
    console.log('Начинаем валидацию токена');
    const validation = await validateToken(token);
    console.log('Результат валидации:', validation);

    if (validation.isValid && validation.user) {
      locals.isAuthenticated = true;
      locals.user = validation.user;

      console.log('Пользователь авторизован:', validation.user.email, 'тип:', validation.user.type);
    } else {
      console.log('Токен недействителен');
    }
  } else {
    console.log('Токен не найден в запросе');
  }

  // Продолжаем обработку запроса
  return next();
});
