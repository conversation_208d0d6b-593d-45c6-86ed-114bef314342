---
import { ClientRouter } from 'astro:transitions'
import Header from '@/components/header'
import Footer from '@/components/footer'
import { AuthSync } from '@/components/admin/AuthSync'
import { AccessibilityProvider } from '@/components/accessibility/AccessibilityProvider'
import SEOHead from '@/components/SEOHead.astro'
import RDFaMarkup from '@/components/RDFaMarkup.astro'
import { generateOrganizationJsonLd } from '@/lib/seo-config'
import '../styles/global.css'

export interface Props {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  type?: string
  canonical?: string
  noindex?: boolean
  nofollow?: boolean
  jsonLd?: any
  article?: {
    publishedTime?: string
    modifiedTime?: string
    author?: string
    section?: string
    tags?: string[]
  }
}

const { title, description, keywords, image, type, canonical, noindex, nofollow, jsonLd, article } = Astro.props

// Генерируем структурированные данные организации для всех страниц
const organizationJsonLd = generateOrganizationJsonLd()

// Объединяем JSON-LD данные
const combinedJsonLd = jsonLd ? [organizationJsonLd, jsonLd] : organizationJsonLd
---

<html lang='ru'>
  <head>
    <ClientRouter />
    <meta charset='utf-8' />

    <link rel='icon' type='image/png' href='/favicon-96x96.png' sizes='96x96' />
    <link rel='icon' type='image/svg+xml' href='/favicon.svg' />
    <link rel='shortcut icon' href='/favicon.ico' />
    <link rel='apple-touch-icon' sizes='180x180' href='/apple-touch-icon.png' />
    <meta name='apple-mobile-web-app-title' content='Stom Line' />
    <link rel='manifest' href='/site.webmanifest' />

    <!-- Скрипт инициализации доступности - загружаем первым -->
    <script src='/accessibility-init.js' is:inline></script>

    <!-- SEO мета-теги -->
    <SEOHead
      title={title}
      description={description}
      keywords={keywords}
      image={image}
      type={type}
      canonical={canonical}
      noindex={noindex}
      nofollow={nofollow}
      jsonLd={combinedJsonLd}
      article={article}
    />

    <!-- Шрифты -->
    <link href='https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap' rel='stylesheet' />

    <!-- VK API -->
    <script type="text/javascript" src="https://vk.com/js/api/openapi.js?168" is:inline></script>
  </head>
  <body class='font-montserrat'>
    <!-- Скрытая ссылка для перехода к основному контенту -->
    <a
      href='#main-content'
      class='sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:rounded focus:bg-black focus:px-4 focus:py-2 focus:text-white'
    >
      Перейти к основному содержанию
    </a>

    <!-- <AccessibilityProvider client:load> -->
      <AuthSync client:load />
      <Header client:load />
      <main
        id='main-content'
        role='main'
        aria-label='Основное содержание страницы'
        class='from-[#8BC34A]/5 via-[#85C026]/10 to-[#8BC34A]/5 relative flex min-h-screen flex-col bg-gradient-to-b'
      >
        <slot />
      </main>
      <Footer client:only='react' />
    <!-- </AccessibilityProvider> -->

    <!-- Скрытый элемент для объявлений скринридера -->
    <div id='accessibility-announcements' aria-live='polite' aria-atomic='true' class='sr-only'></div>

    <!-- RDFa разметка для поисковых систем -->
    <RDFaMarkup type='organization' />
  </body>
</html>
