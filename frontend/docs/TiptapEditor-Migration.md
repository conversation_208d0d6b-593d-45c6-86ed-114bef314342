# Миграция на новый TiptapEditorWrapper

## Что изменилось

### ✅ Улучшения
- **Современный UI** с группированными инструментами
- **Режим исходного кода** для прямого редактирования HTML
- **Автосохранение** с индикацией статуса
- **Загрузка файлов** через PocketBase
- **Адаптивный дизайн** для мобильных устройств
- **Корпоративные цвета** (#8BC34A, #4E8C29, #85C026)
- **Улучшенная типизация** TypeScript

### 🔄 Изменения в API

#### Новые свойства
```tsx
interface TiptapEditorWrapperProps {
  // Новые свойства
  placeholder?: string
  className?: string
  autoSave?: boolean
  onAutoSave?: (content: string) => Promise<void> | void
  autoSaveInterval?: number
  
  // Существующие свойства (без изменений)
  id?: string
  value: string
  onChange: (value: string) => void
  readOnly?: boolean
  style?: React.CSSProperties
}
```

#### Удаленные элементы
- Старые кнопки с эмодзи заменены на иконки Lucide
- Простые prompt() диалоги заменены на модальные окна
- Inline стили заменены на CSS классы

## Пошаговая миграция

### Шаг 1: Обновление импортов
```tsx
// Старый код
import { TiptapEditorWrapper } from '@/components/admin/TiptapEditorWrapper'

// Новый код (импорт остается тем же)
import { TiptapEditorWrapper } from '@/components/admin/TiptapEditorWrapper'
```

### Шаг 2: Обновление использования

#### Базовое использование (без изменений)
```tsx
// Работает как раньше
<TiptapEditorWrapper
  value={content}
  onChange={setContent}
/>
```

#### Добавление новых возможностей
```tsx
// Добавляем новые функции
<TiptapEditorWrapper
  value={content}
  onChange={setContent}
  placeholder="Введите описание..."
  className="my-editor"
  autoSave={true}
  onAutoSave={handleAutoSave}
  autoSaveInterval={3000}
/>
```

### Шаг 3: Реализация автосохранения

```tsx
// Новая функция автосохранения
const handleAutoSave = async (content: string) => {
  try {
    await pb.collection('articles').update(recordId, { content })
    console.log('Контент автоматически сохранен')
  } catch (error) {
    console.error('Ошибка автосохранения:', error)
    throw error // Для отображения ошибки в UI
  }
}
```

### Шаг 4: Обновление стилей

#### Удалите старые кастомные стили
```css
/* Удалите эти стили, если они есть */
.tiptap-editor .px-2 { ... }
.tiptap-editor .border-b-4 { ... }
```

#### Добавьте новый CSS файл
```tsx
// CSS автоматически импортируется в компоненте
import './TiptapEditor.css'
```

## Совместимость

### ✅ Обратная совместимость
- Все существующие свойства работают без изменений
- HTML-контент остается совместимым
- Существующие обработчики onChange работают как раньше

### ⚠️ Визуальные изменения
- Новый дизайн панели инструментов
- Другие иконки (Lucide вместо эмодзи)
- Модальные диалоги вместо prompt()
- Корпоративные цвета

## Примеры миграции

### До (старый код)
```tsx
function ArticleEditor({ article, onSave }) {
  const [content, setContent] = useState(article.content)
  
  const handleSave = () => {
    onSave({ ...article, content })
  }
  
  return (
    <div>
      <TiptapEditorWrapper
        value={content}
        onChange={setContent}
        style={{ minHeight: '300px' }}
      />
      <button onClick={handleSave}>Сохранить</button>
    </div>
  )
}
```

### После (новый код)
```tsx
function ArticleEditor({ article, onSave }) {
  const [content, setContent] = useState(article.content)
  
  const handleAutoSave = async (newContent: string) => {
    // Автоматическое сохранение каждые 5 секунд
    await onSave({ ...article, content: newContent })
  }
  
  const handleManualSave = () => {
    onSave({ ...article, content })
  }
  
  return (
    <div>
      <TiptapEditorWrapper
        value={content}
        onChange={setContent}
        placeholder="Введите содержание статьи..."
        autoSave={true}
        onAutoSave={handleAutoSave}
        autoSaveInterval={5000}
        className="min-h-[300px]"
      />
      <button onClick={handleManualSave}>Сохранить вручную</button>
    </div>
  )
}
```

## Тестирование после миграции

### Проверьте основные функции
1. ✅ Ввод и редактирование текста
2. ✅ Форматирование (жирный, курсив, заголовки)
3. ✅ Списки и таблицы
4. ✅ Ссылки и изображения
5. ✅ Сохранение контента

### Проверьте новые функции
1. ✅ Переключение в режим кода
2. ✅ Автосохранение (если включено)
3. ✅ Загрузка изображений
4. ✅ Модальные диалоги
5. ✅ Мобильная адаптивность

### Проверьте интеграцию
1. ✅ Работа с PocketBase
2. ✅ Обработка ошибок
3. ✅ Производительность
4. ✅ Совместимость с браузерами

## Устранение проблем

### Проблема: Старые стили конфликтуют
**Решение**: Удалите кастомные CSS правила для `.tiptap-editor`

### Проблема: Автосохранение не работает
**Решение**: Проверьте, что `onAutoSave` возвращает Promise

### Проблема: Изображения не загружаются
**Решение**: Убедитесь, что PocketBase доступен и настроен

### Проблема: Мобильный интерфейс
**Решение**: Проверьте viewport meta tag и CSS

## Поддержка

Если возникают проблемы при миграции:
1. Проверьте консоль браузера на ошибки
2. Убедитесь, что все зависимости обновлены
3. Проверьте совместимость с существующими компонентами
4. Обратитесь к документации в `TiptapEditor-README.md`

---

Миграция должна пройти гладко благодаря обратной совместимости. Новые функции можно добавлять постепенно.
