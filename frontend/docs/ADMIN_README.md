# Админ-панель Стом-Лайн

## Функционал создания записей

Теперь в админ-панели доступен полноценный функционал создания новых записей во всех коллекциях PocketBase.

### Как создать новую запись

#### Способ 1: Через главную панель
1. Перейдите в админ-панель `/admin`
2. Авторизуйтесь
3. Нажмите кнопку "Создать запись" в разделе "Быстрые действия"
4. Выберите нужную коллекцию
5. Заполните форму и нажмите "Создать"

#### Способ 2: Через страницу коллекции
1. Перейдите в админ-панель `/admin`
2. Выберите нужную коллекцию (например, "Специалисты")
3. Нажмите кнопку "Создать" в правом верхнем углу
4. Заполните форму и нажмите "Создать"

#### Способ 3: Через карточки коллекций на главной
1. На главной странице админ-панели найдите нужную коллекцию
2. Нажмите кнопку "+" рядом с кнопкой "Управлять"
3. Заполните форму и нажмите "Создать"

### Доступные коллекции для создания

- **Специалисты** (`doctors`) - врачи и медицинские специалисты
- **Услуги** (`services`) - медицинские услуги клиники
- **Категории услуг** (`service_categories`) - группировка услуг
- **Прайс-лист** (`prices`) - цены на услуги
- **Отзывы** (`reviews`) - отзывы пациентов
- **FAQ** (`faq`) - часто задаваемые вопросы
- **Новости** (`news`) - новости и статьи
- **Акции** (`promos`) - специальные предложения
- **Страницы** (`pages`) - статические страницы сайта
- **HTML-блоки** (`html_blocks`) - переиспользуемые HTML-блоки
- **Персонал** (`personal`) - сотрудники клиники

### Особенности формы создания

1. **Автоматическая валидация** - форма проверяет обязательные поля
2. **Поддержка файлов** - можно загружать изображения и документы
3. **Связанные записи** - выбор из существующих записей других коллекций
4. **HTML-редактор** - для полей с HTML-контентом
5. **Автоматическое перенаправление** - после создания записи вы попадете на страницу редактирования

### Технические детали

- Все записи создаются через PocketBase API
- Поддерживается загрузка файлов через FormData
- Автоматически устанавливаются поля `created` и `updated`
- ID записи генерируется автоматически

### Безопасность

- Требуется авторизация через PocketBase
- Проверка прав доступа на уровне middleware
- Валидация данных на стороне сервера

### Устранение неполадок

**Ошибка "Не найден токен авторизации"**
- Убедитесь, что вы авторизованы в админ-панели
- Попробуйте выйти и войти заново

**Ошибка "Collection not allowed for creation"**
- Коллекция не включена в список разрешенных для создания
- Обратитесь к администратору для добавления коллекции

**Ошибка при сохранении**
- Проверьте заполнение обязательных полей
- Убедитесь, что файлы не превышают максимальный размер
- Проверьте корректность данных в связанных полях

### Архитектура работы со схемой PocketBase

**Унифицированный доступ к схеме:**
- Файл схемы: `frontend/public/pb_schema.json`
- Утилита: `frontend/src/lib/pocketbase-schema.ts`
- Кеширование: 5 минут на сервере и в браузере
- Автоматическое определение окружения (сервер/клиент)

**Серверная сторона (Astro):**
- Использует `node:fs` для чтения файла
- Кеширует схему в памяти на 5 минут
- Автоматический выбор пути:
  - Разработка: `process.cwd()/public/pb_schema.json`
  - Продакшен: `process.cwd()/dist/client/pb_schema.json`

**Клиентская сторона (React):**
- Использует `fetch('/pb_schema.json')`
- Кеширует схему в памяти браузера
- Автоматическая очистка кеша при обновлении

**Основные функции:**
- `loadPocketBaseSchema()` - загрузка полной схемы
- `getCollectionSchema(name)` - схема конкретной коллекции
- `getCollectionFields(name, options)` - поля с фильтрацией
- `isCollectionCreateAllowed(name)` - проверка разрешений
- `clearSchemaCache()` - очистка кеша

### Дальнейшее развитие

Планируется добавить:
- Массовое создание записей
- Шаблоны для быстрого создания
- Предварительный просмотр
- Автосохранение черновиков
- API endpoint для получения схемы
- Валидация полей на основе схемы
