# Современный HTML-редактор TiptapEditorWrapper

## Обзор

Полностью переработанный компонент `TiptapEditorWrapper` представляет собой современный HTML-редактор с интуитивным интерфейсом, созданный специально для модераторов контента стоматологической клиники.

## Основные возможности

### 🎨 Современный дизайн
- Корпоративные цвета (#8BC34A, #4E8C29, #85C026)
- Адаптивный интерфейс для мобильных устройств
- Интуитивно понятная панель инструментов с иконками
- Группировка инструментов по категориям

### ✨ Функциональность
- **Визуальное редактирование** - WYSIWYG интерфейс
- **Режим исходного кода** - прямое редактирование HTML
- **Автосохранение** с индикацией статуса
- **Загрузка файлов** через PocketBase
- **Отмена/повтор** действий
- **Предварительный просмотр** результата

### 📝 Поддерживаемые элементы
- Заголовки (H1-H6)
- Абзацы и форматирование текста
- Списки (маркированные, нумерованные, задачи)
- Ссылки и изображения
- Таблицы с управлением
- Цитаты и блоки кода
- Горизонтальные линии
- Цвета текста и выделение

## Использование

### Базовое использование

```tsx
import { TiptapEditorWrapper } from '@/components/admin/TiptapEditorWrapper'

function MyComponent() {
  const [content, setContent] = useState('<p>Начальный контент</p>')

  return (
    <TiptapEditorWrapper
      value={content}
      onChange={setContent}
      placeholder="Введите текст..."
    />
  )
}
```

### С автосохранением

```tsx
const handleAutoSave = async (content: string) => {
  // Сохранение в PocketBase или другую систему
  await pb.collection('articles').update(recordId, { content })
}

<TiptapEditorWrapper
  value={content}
  onChange={setContent}
  autoSave={true}
  onAutoSave={handleAutoSave}
  autoSaveInterval={5000} // 5 секунд
/>
```

### Только для чтения

```tsx
<TiptapEditorWrapper
  value={content}
  onChange={() => {}} // Пустая функция
  readOnly={true}
/>
```

## Свойства компонента

| Свойство | Тип | По умолчанию | Описание |
|----------|-----|--------------|----------|
| `value` | `string` | - | HTML-контент для редактирования |
| `onChange` | `(value: string) => void` | - | Callback при изменении контента |
| `id` | `string?` | - | ID элемента |
| `readOnly` | `boolean?` | `false` | Режим только для чтения |
| `placeholder` | `string?` | `"Начните вводить текст..."` | Placeholder текст |
| `className` | `string?` | - | Дополнительные CSS классы |
| `style` | `React.CSSProperties?` | `{}` | Inline стили |
| `autoSave` | `boolean?` | `false` | Включить автосохранение |
| `onAutoSave` | `(content: string) => Promise<void> \| void` | - | Callback автосохранения |
| `autoSaveInterval` | `number?` | `5000` | Интервал автосохранения (мс) |

## Панель инструментов

### Форматирование
- **Отмена/Повтор** - управление историей изменений
- **Базовое форматирование** - жирный, курсив, подчеркнутый, зачеркнутый
- **Выравнивание** - по левому краю, центру, правому краю, ширине
- **Цвета** - цвет текста и выделение фона

### Структура
- **Заголовки** - H1, H2, H3 и обычный текст
- **Списки** - маркированные, нумерованные, задачи
- **Блоки** - цитаты, код, горизонтальные линии

### Медиа
- **Ссылки** - добавление и удаление ссылок
- **Изображения** - вставка по URL или загрузка файла
- **Таблицы** - создание и управление таблицами

### Дополнительно
- **Индексы** - верхний и нижний индекс
- **Очистка** - сброс форматирования
- **Режимы** - переключение между визуальным и кодовым режимом

## Интеграция с PocketBase

Редактор поддерживает загрузку изображений через PocketBase:

```tsx
// Автоматическая загрузка файлов при выборе
// Файлы сохраняются в коллекцию 'files'
// При ошибке используется локальный URL как fallback
```

## Стилизация

Редактор использует CSS-файл `TiptapEditor.css` с:
- Корпоративными цветами
- Адаптивным дизайном
- Анимациями и переходами
- Поддержкой темной темы

## Горячие клавиши

- `Ctrl/Cmd + B` - жирный текст
- `Ctrl/Cmd + I` - курсив
- `Ctrl/Cmd + U` - подчеркивание
- `Ctrl/Cmd + Z` - отмена
- `Ctrl/Cmd + Y` - повтор
- `Ctrl/Cmd + K` - добавить ссылку

## Мобильная адаптивность

- Минимальный размер тач-элементов 44px
- Адаптивная панель инструментов
- Оптимизированные размеры шрифтов
- Предотвращение зума на iOS (font-size: 16px)

## Автосохранение

Система автосохранения включает:
- Индикатор статуса (сохранение, сохранено, ошибка)
- Настраиваемый интервал
- Обработка ошибок
- Автоматическая очистка таймеров

## Примеры использования

### В админ-панели
```tsx
// В компоненте редактирования статьи
<TiptapEditorWrapper
  value={article.content}
  onChange={(content) => setArticle({...article, content})}
  autoSave={true}
  onAutoSave={async (content) => {
    await updateArticle(article.id, { content })
  }}
/>
```

### В форме создания контента
```tsx
// В форме создания новой записи
<TiptapEditorWrapper
  value={formData.description}
  onChange={(description) => setFormData({...formData, description})}
  placeholder="Введите описание услуги..."
/>
```

## Технические детали

- **Основа**: Tiptap v2.14.0
- **UI**: shadcn/ui компоненты
- **Иконки**: Lucide React
- **Стили**: Tailwind CSS + кастомный CSS
- **TypeScript**: Полная типизация
- **Расширения**: 20+ Tiptap расширений

## Поддержка браузеров

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Производительность

- Ленивая загрузка расширений
- Оптимизированные ререндеры
- Дебаунс автосохранения
- Минимальный размер бандла

---

Редактор готов к использованию в production и полностью интегрирован с существующей системой компонентов проекта.
