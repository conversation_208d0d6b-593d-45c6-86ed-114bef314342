# Рендеринг HTML в блоках контента

## Обзор

Функция `getBlockContent` была обновлена для поддержки рендеринга HTML контента. Теперь в блоках контента можно использовать HTML разметку, которая будет безопасно отображаться на странице.

## Новые функции

### `renderBlockContent(key, fallback, className?)`

Новая функция для рендеринга контента блоков с поддержкой HTML:

- **key**: ключ блока в PocketBase
- **fallback**: текст по умолчанию, если блок не найден
- **className**: опциональные CSS классы для контейнера

### `HtmlContent` компонент

Безопасный компонент для рендеринга HTML контента:

```tsx
<HtmlContent content={htmlString} className="optional-classes" />
```

## Как это работает

1. Функция `renderBlockContent` проверяет тип блока в PocketBase
2. Если тип блока `'html'`, контент рендерится как HTML через `dangerouslySetInnerHTML`
3. Если тип блока любой другой, контент рендерится как обычный текст
4. Это обеспечивает безопасность - HTML рендерится только для специально помеченных блоков

## Использование в PocketBase

### Создание HTML блока

1. В админ-панели PocketBase создайте новый блок в коллекции `html_blocks`
2. Установите поле `type` в значение `'html'`
3. В поле `content` добавьте HTML разметку

### Пример HTML блока

```json
{
  "key": "about_description",
  "title": "Описание о нас",
  "content": "<p>Мы <strong>лучшая</strong> стоматологическая клиника!</p><ul><li>Опытные врачи</li><li>Современное оборудование</li></ul>",
  "type": "html",
  "section": "about"
}
```

## Поддерживаемые HTML теги

Рекомендуется использовать следующие безопасные HTML теги:

- `<p>`, `<div>`, `<span>` - контейнеры
- `<strong>`, `<em>`, `<b>`, `<i>` - форматирование текста
- `<ul>`, `<ol>`, `<li>` - списки
- `<a href="#">` - ссылки
- `<br>` - переносы строк
- `<h1>` - `<h6>` - заголовки (с осторожностью)

## Стилизация

Для HTML контента можно использовать:

1. **Inline стили**: `<span style="color: #4E8C29;">цветной текст</span>`
2. **CSS классы**: передать `className` в `renderBlockContent`
3. **Глобальные стили**: стили применятся автоматически

## Примеры использования

### Простой текст с форматированием

```html
<p>Наша клиника работает с <strong>2008 года</strong>. Мы принимаем <em>более 2500 пациентов</em> в год.</p>
```

### Список услуг

```html
<ul>
  <li>Терапевтическая стоматология</li>
  <li>Хирургическая стоматология</li>
  <li>Ортопедическая стоматология</li>
  <li>Имплантология</li>
</ul>
```

### Текст с ссылкой

```html
<p>Запишитесь на <a href="/appointment">бесплатную консультацию</a> уже сегодня!</p>
```

## Безопасность

- HTML рендерится только для блоков с типом `'html'`
- Используется `dangerouslySetInnerHTML`, поэтому контент должен быть проверен
- Рекомендуется ограничить доступ к редактированию HTML блоков только доверенным пользователям
- Избегайте использования `<script>` тегов и других потенциально опасных элементов

## Миграция существующих блоков

Существующие блоки продолжат работать как обычно. Для включения HTML рендеринга:

1. Измените тип блока с `'text'` на `'html'` в PocketBase
2. Обновите контент блока, добавив HTML разметку
3. Изменения применятся автоматически

## Отладка

Если HTML не рендерится:

1. Проверьте, что тип блока установлен в `'html'`
2. Убедитесь, что HTML разметка корректна
3. Проверьте консоль браузера на наличие ошибок
4. Используйте инструменты разработчика для проверки DOM
