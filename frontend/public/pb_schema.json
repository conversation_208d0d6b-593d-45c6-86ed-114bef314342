[{"id": "pbc_3142635823", "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "name": "_superusers", "type": "auth", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cost": 0, "hidden": true, "id": "password901924565", "max": 0, "min": 8, "name": "password", "pattern": "", "presentable": false, "required": true, "system": true, "type": "password"}, {"autogeneratePattern": "[a-zA-Z0-9]{50}", "hidden": true, "id": "text2504183744", "max": 60, "min": 30, "name": "<PERSON><PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"exceptDomains": null, "hidden": false, "id": "email3885137012", "name": "email", "onlyDomains": null, "presentable": false, "required": true, "system": true, "type": "email"}, {"hidden": false, "id": "bool1547992806", "name": "emailVisibility", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "bool256245529", "name": "verified", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": true, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": true, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_tokenKey_pbc_3142635823` ON `_superusers` (`tokenKey`)", "CREATE UNIQUE INDEX `idx_email_pbc_3142635823` ON `_superusers` (`email`) WHERE `email` != ''"], "system": true, "authRule": "", "manageRule": null, "authAlert": {"enabled": true, "emailTemplate": {"subject": "Login from a new location", "body": "<p>Hello,</p>\n<p>We noticed a login to your {APP_NAME} account from a new location.</p>\n<p>If this was you, you may disregard this email.</p>\n<p><strong>If this wasn't you, you should immediately change your {APP_NAME} account password to revoke access from all other locations.</strong></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "oauth2": {"mappedFields": {"id": "", "name": "", "username": "", "avatarURL": ""}, "enabled": false}, "passwordAuth": {"enabled": true, "identityFields": ["email"]}, "mfa": {"enabled": false, "duration": 1800, "rule": ""}, "otp": {"enabled": false, "duration": 180, "length": 8, "emailTemplate": {"subject": "OTP for {APP_NAME}", "body": "<p>Hello,</p>\n<p>Your one-time password is: <strong>{OTP}</strong></p>\n<p><i>If you didn't ask for the one-time password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "authToken": {"duration": 86400}, "passwordResetToken": {"duration": 1800}, "emailChangeToken": {"duration": 1800}, "verificationToken": {"duration": 259200}, "fileToken": {"duration": 180}, "verificationTemplate": {"subject": "Verify your {APP_NAME} email", "body": "<p>Hello,</p>\n<p>Thank you for joining us at {APP_NAME}.</p>\n<p>Click on the button below to verify your email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-verification/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Verify</a>\n</p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "resetPasswordTemplate": {"subject": "Reset your {APP_NAME} password", "body": "<p>Hello,</p>\n<p>Click on the button below to reset your password.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-password-reset/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Reset password</a>\n</p>\n<p><i>If you didn't ask to reset your password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "confirmEmailChangeTemplate": {"subject": "Confirm your {APP_NAME} new email address", "body": "<p>Hello,</p>\n<p>Click on the button below to confirm your new email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-email-change/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Confirm new email</a>\n</p>\n<p><i>If you didn't ask to change your email address, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, {"id": "_pb_users_auth_", "listRule": "id = @request.auth.id", "viewRule": "id = @request.auth.id", "createRule": "", "updateRule": "id = @request.auth.id", "deleteRule": "id = @request.auth.id", "name": "users", "type": "auth", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cost": 0, "hidden": true, "id": "password901924565", "max": 0, "min": 8, "name": "password", "pattern": "", "presentable": false, "required": true, "system": true, "type": "password"}, {"autogeneratePattern": "[a-zA-Z0-9]{50}", "hidden": true, "id": "text2504183744", "max": 60, "min": 30, "name": "<PERSON><PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"exceptDomains": null, "hidden": false, "id": "email3885137012", "name": "email", "onlyDomains": null, "presentable": false, "required": true, "system": true, "type": "email"}, {"hidden": false, "id": "bool1547992806", "name": "emailVisibility", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "bool256245529", "name": "verified", "presentable": false, "required": false, "system": true, "type": "bool"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 255, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file376926767", "maxSelect": 1, "maxSize": 0, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "name": "avatar", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_tokenKey__pb_users_auth_` ON `users` (`tokenKey`)", "CREATE UNIQUE INDEX `idx_email__pb_users_auth_` ON `users` (`email`) WHERE `email` != ''"], "system": false, "authRule": "", "manageRule": null, "authAlert": {"enabled": true, "emailTemplate": {"subject": "Login from a new location", "body": "<p>Hello,</p>\n<p>We noticed a login to your {APP_NAME} account from a new location.</p>\n<p>If this was you, you may disregard this email.</p>\n<p><strong>If this wasn't you, you should immediately change your {APP_NAME} account password to revoke access from all other locations.</strong></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "oauth2": {"mappedFields": {"id": "", "name": "name", "username": "", "avatarURL": "avatar"}, "enabled": false}, "passwordAuth": {"enabled": true, "identityFields": ["email"]}, "mfa": {"enabled": false, "duration": 1800, "rule": ""}, "otp": {"enabled": false, "duration": 180, "length": 8, "emailTemplate": {"subject": "OTP for {APP_NAME}", "body": "<p>Hello,</p>\n<p>Your one-time password is: <strong>{OTP}</strong></p>\n<p><i>If you didn't ask for the one-time password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "authToken": {"duration": 604800}, "passwordResetToken": {"duration": 1800}, "emailChangeToken": {"duration": 1800}, "verificationToken": {"duration": 259200}, "fileToken": {"duration": 180}, "verificationTemplate": {"subject": "Verify your {APP_NAME} email", "body": "<p>Hello,</p>\n<p>Thank you for joining us at {APP_NAME}.</p>\n<p>Click on the button below to verify your email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-verification/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Verify</a>\n</p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "resetPasswordTemplate": {"subject": "Reset your {APP_NAME} password", "body": "<p>Hello,</p>\n<p>Click on the button below to reset your password.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-password-reset/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Reset password</a>\n</p>\n<p><i>If you didn't ask to reset your password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "confirmEmailChangeTemplate": {"subject": "Confirm your {APP_NAME} new email address", "body": "<p>Hello,</p>\n<p>Click on the button below to confirm your new email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-email-change/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Confirm new email</a>\n</p>\n<p><i>If you didn't ask to change your email address, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, {"id": "pbc_4275539003", "listRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "viewRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "createRule": null, "updateRule": null, "deleteRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "name": "_auth<PERSON><PERSON><PERSON>", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text455797646", "max": 0, "min": 0, "name": "collectionRef", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text127846527", "max": 0, "min": 0, "name": "recordRef", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text4228609354", "max": 0, "min": 0, "name": "fingerprint", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": true, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": true, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_authOrigins_unique_pairs` ON `_authOrigins` (collectionRef, recordRef, fingerprint)"], "system": true}, {"id": "pbc_2281828961", "listRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "viewRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "createRule": null, "updateRule": null, "deleteRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "name": "_externalAuths", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text455797646", "max": 0, "min": 0, "name": "collectionRef", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text127846527", "max": 0, "min": 0, "name": "recordRef", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text2462348188", "max": 0, "min": 0, "name": "provider", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1044722854", "max": 0, "min": 0, "name": "providerId", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": true, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": true, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_externalAuths_record_provider` ON `_externalAuths` (collectionRef, recordRef, provider)", "CREATE UNIQUE INDEX `idx_externalAuths_collection_provider` ON `_externalAuths` (collectionRef, provider, providerId)"], "system": true}, {"id": "pbc_2279338944", "listRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "viewRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "createRule": null, "updateRule": null, "deleteRule": null, "name": "_mfas", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text455797646", "max": 0, "min": 0, "name": "collectionRef", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text127846527", "max": 0, "min": 0, "name": "recordRef", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1582905952", "max": 0, "min": 0, "name": "method", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": true, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": true, "type": "autodate"}], "indexes": ["CREATE INDEX `idx_mfas_collectionRef_recordRef` ON `_mfas` (collectionRef,recordRef)"], "system": true}, {"id": "pbc_1638494021", "listRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "viewRule": "@request.auth.id != '' && recordRef = @request.auth.id && collectionRef = @request.auth.collectionId", "createRule": null, "updateRule": null, "deleteRule": null, "name": "_otps", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text455797646", "max": 0, "min": 0, "name": "collectionRef", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text127846527", "max": 0, "min": 0, "name": "recordRef", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"cost": 8, "hidden": true, "id": "password901924565", "max": 0, "min": 0, "name": "password", "pattern": "", "presentable": false, "required": true, "system": true, "type": "password"}, {"autogeneratePattern": "", "hidden": true, "id": "text3866985172", "max": 0, "min": 0, "name": "sentTo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": true, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": true, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": true, "type": "autodate"}], "indexes": ["CREATE INDEX `idx_otps_collectionRef_recordRef` ON `_otps` (collectionRef, recordRef)"], "system": true}, {"id": "pbc_3434335328", "listRule": null, "viewRule": null, "createRule": "", "updateRule": null, "deleteRule": null, "name": "callback_requests", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1146066909", "max": 0, "min": 0, "name": "phone", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3065852031", "max": 0, "min": 0, "name": "message", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text587191692", "max": 0, "min": 0, "name": "ip_address", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool509848140", "name": "isProcessed", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_doctors", "listRule": "", "viewRule": "", "createRule": null, "updateRule": null, "deleteRule": null, "name": "doctors", "type": "base", "fields": [{"autogeneratePattern": "", "hidden": false, "id": "text3208210256", "max": 0, "min": 0, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "surname", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990060", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990061", "max": 0, "min": 0, "name": "patronymic", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990062", "max": 0, "min": 0, "name": "slug", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990063", "max": 0, "min": 0, "name": "position", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"convertURLs": false, "hidden": false, "id": "editor724990064", "maxSize": 0, "name": "short_description", "presentable": false, "required": false, "system": false, "type": "editor"}, {"convertURLs": false, "hidden": false, "id": "editor724990065", "maxSize": 0, "name": "biography", "presentable": false, "required": false, "system": false, "type": "editor"}, {"hidden": false, "id": "file724990066", "maxSelect": 0, "maxSize": 0, "mimeTypes": null, "name": "photo", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"cascadeDelete": false, "collectionId": "pbc_service_categories", "hidden": false, "id": "relation724990067", "maxSelect": 0, "minSelect": 0, "name": "specializations", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_services", "hidden": false, "id": "relation724990068", "maxSelect": 0, "minSelect": 0, "name": "services", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990069", "max": 0, "min": 0, "name": "experience", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990070", "max": 0, "min": 0, "name": "clinic", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990071", "max": 0, "min": 0, "name": "meta_title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990072", "max": 0, "min": 0, "name": "meta_description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool724990073", "name": "is_featured", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "number724990074", "max": null, "min": null, "name": "sort_order", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"cascadeDelete": false, "collectionId": "pbc_3446931122", "hidden": false, "id": "relation2368142175", "maxSelect": 1, "minSelect": 0, "name": "certificates", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": false, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_faq", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "faq", "type": "base", "fields": [{"autogeneratePattern": "", "hidden": false, "id": "text3208210256", "max": 0, "min": 0, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "question", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"convertURLs": false, "hidden": false, "id": "editor724990060", "maxSize": 0, "name": "answer", "presentable": false, "required": false, "system": false, "type": "editor"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990061", "max": 0, "min": 0, "name": "category", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool724990062", "name": "is_published", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "number724990063", "max": null, "min": null, "name": "sort_order", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": false, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3446931122", "listRule": "", "viewRule": "", "createRule": null, "updateRule": null, "deleteRule": null, "name": "files", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["certificate", "any", "image", "document"]}, {"hidden": false, "id": "file2359244304", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"cascadeDelete": false, "collectionId": "pbc_doctors", "hidden": false, "id": "relation532738922", "maxSelect": 1, "minSelect": 0, "name": "doctor", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "[a-z0-9]{20}", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_236568197", "listRule": "", "viewRule": "", "createRule": null, "updateRule": null, "deleteRule": null, "name": "html_blocks", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "key", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"convertURLs": false, "hidden": false, "id": "editor4274335913", "maxSize": 0, "name": "content", "presentable": false, "required": false, "system": false, "type": "editor"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text762542831", "max": 0, "min": 0, "name": "section", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text2363381545", "max": 0, "min": 0, "name": "type", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool458715613", "name": "is_active", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "number1169138922", "max": null, "min": null, "name": "sort_order", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_news", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "news", "type": "base", "fields": [{"autogeneratePattern": "", "hidden": false, "id": "text3208210256", "max": 0, "min": 0, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990060", "max": 0, "min": 0, "name": "slug", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"hidden": false, "id": "date724990061", "max": "", "min": "", "name": "date", "presentable": false, "required": true, "system": false, "type": "date"}, {"convertURLs": false, "hidden": false, "id": "editor724990062", "maxSize": 0, "name": "content", "presentable": false, "required": false, "system": false, "type": "editor"}, {"hidden": false, "id": "file724990063", "maxSelect": 0, "maxSize": 0, "mimeTypes": null, "name": "image", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990064", "max": 0, "min": 0, "name": "meta_title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990065", "max": 0, "min": 0, "name": "meta_description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool724990066", "name": "is_featured", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": false, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_pages", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "pages", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{10}", "hidden": false, "id": "text3208210256", "max": 0, "min": 0, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990060", "max": 0, "min": 0, "name": "slug", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"convertURLs": false, "hidden": false, "id": "editor724990061", "maxSize": 0, "name": "content", "presentable": false, "required": false, "system": false, "type": "editor"}, {"hidden": false, "id": "file724990062", "maxSelect": 0, "maxSize": 0, "mimeTypes": null, "name": "featured_image", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"hidden": false, "id": "file724990063", "maxSelect": 0, "maxSize": 0, "mimeTypes": null, "name": "gallery", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990064", "max": 0, "min": 0, "name": "parent_slug", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990065", "max": 0, "min": 0, "name": "meta_title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990066", "max": 0, "min": 0, "name": "meta_description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool724990067", "name": "is_published", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "number724990068", "max": null, "min": null, "name": "sort_order", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": false, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2982426013", "listRule": "", "viewRule": "", "createRule": null, "updateRule": null, "deleteRule": null, "name": "personal", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3883309839", "max": 0, "min": 0, "name": "surname", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3827959211", "max": 0, "min": 0, "name": "patronymic", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"convertURLs": false, "hidden": false, "id": "editor3052675811", "maxSize": 0, "name": "about", "presentable": false, "required": false, "system": false, "type": "editor"}, {"autogeneratePattern": "", "hidden": false, "id": "text1177347317", "max": 0, "min": 0, "name": "position", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_prices", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "prices", "type": "base", "fields": [{"autogeneratePattern": "", "hidden": false, "id": "text3208210256", "max": 0, "min": 0, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_service_categories", "hidden": false, "id": "relation724990060", "maxSelect": 1, "minSelect": 0, "name": "category", "presentable": false, "required": true, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_services", "hidden": false, "id": "relation724990061", "maxSelect": 1, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "number724990062", "max": null, "min": null, "name": "price", "onlyInt": false, "presentable": false, "required": true, "system": false, "type": "number"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990063", "max": 0, "min": 0, "name": "price_suffix", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990064", "max": 0, "min": 0, "name": "price_prefix", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990065", "max": 0, "min": 0, "name": "unit", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool724990066", "name": "is_active", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "number724990067", "max": null, "min": null, "name": "sort_order", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": false, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_promos", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "promos", "type": "base", "fields": [{"autogeneratePattern": "", "hidden": false, "id": "text3208210256", "max": 0, "min": 0, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990060", "max": 0, "min": 0, "name": "subtitle", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990061", "max": 0, "min": 0, "name": "slug", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"hidden": false, "id": "date724990062", "max": "", "min": "", "name": "start_date", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date724990063", "max": "", "min": "", "name": "end_date", "presentable": false, "required": false, "system": false, "type": "date"}, {"convertURLs": false, "hidden": false, "id": "editor724990064", "maxSize": 0, "name": "content", "presentable": false, "required": false, "system": false, "type": "editor"}, {"hidden": false, "id": "file724990065", "maxSelect": 0, "maxSize": 0, "mimeTypes": null, "name": "image", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"cascadeDelete": false, "collectionId": "pbc_services", "hidden": false, "id": "relation724990066", "maxSelect": 0, "minSelect": 0, "name": "related_services", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990067", "max": 0, "min": 0, "name": "meta_title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990068", "max": 0, "min": 0, "name": "meta_description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool724990069", "name": "is_active", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "bool724990070", "name": "is_featured", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "number724990071", "max": null, "min": null, "name": "sort_order", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": false, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_reviews", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "reviews", "type": "base", "fields": [{"autogeneratePattern": "", "hidden": false, "id": "text3208210256", "max": 0, "min": 0, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "author", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"hidden": false, "id": "text724990060", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990061", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"convertURLs": false, "hidden": false, "id": "editor724990062", "maxSize": 0, "name": "content", "presentable": false, "required": false, "system": false, "type": "editor"}, {"hidden": false, "id": "file724990063", "maxSelect": 0, "maxSize": 0, "mimeTypes": null, "name": "image", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"hidden": false, "id": "bool724990064", "name": "is_published", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "number724990065", "max": null, "min": null, "name": "sort_order", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": false, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_service_categories", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "service_categories", "type": "base", "fields": [{"autogeneratePattern": "", "hidden": false, "id": "text3208210256", "max": 0, "min": 0, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990060", "max": 0, "min": 0, "name": "slug", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"convertURLs": false, "hidden": false, "id": "editor724990061", "maxSize": 0, "name": "description", "presentable": false, "required": false, "system": false, "type": "editor"}, {"hidden": false, "id": "file724990062", "maxSelect": 0, "maxSize": 0, "mimeTypes": null, "name": "image", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990063", "max": 0, "min": 0, "name": "meta_title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990064", "max": 0, "min": 0, "name": "meta_description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": false, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_services", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "services", "type": "base", "fields": [{"autogeneratePattern": "", "hidden": false, "id": "text3208210256", "max": 0, "min": 0, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": true, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990060", "max": 0, "min": 0, "name": "slug", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_service_categories", "hidden": false, "id": "relation724990061", "maxSelect": 1, "minSelect": 0, "name": "category", "presentable": false, "required": true, "system": false, "type": "relation"}, {"convertURLs": false, "hidden": false, "id": "editor724990062", "maxSize": 0, "name": "short_description", "presentable": false, "required": false, "system": false, "type": "editor"}, {"convertURLs": false, "hidden": false, "id": "editor724990063", "maxSize": 0, "name": "content", "presentable": false, "required": false, "system": false, "type": "editor"}, {"hidden": false, "id": "file724990064", "maxSelect": 0, "maxSize": 0, "mimeTypes": null, "name": "image", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990065", "max": 0, "min": 0, "name": "meta_title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990066", "max": 0, "min": 0, "name": "meta_description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool724990067", "name": "is_featured", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "number724990068", "max": null, "min": null, "name": "sort_order", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": false, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}]