import React from 'react';

/**
 * Компонент для синхронизации токена авторизации между localStorage и cookies
 * Это необходимо для работы серверного middleware
 */
export const AuthSync: React.FC = () => {
  React.useEffect(() => {
    const syncToken = () => {
      const token = localStorage.getItem('pb_token');

      if (token) {
        // Устанавливаем cookie с токеном
        document.cookie = `pb_token=${token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        console.log('Токен синхронизирован с cookies для middleware');
      } else {
        // Удаляем cookie если токена нет
        document.cookie = 'pb_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        console.log('Токен удален из cookies');
      }
    };

    // Синхронизируем при загрузке
    syncToken();

    // Слушаем изменения в localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'pb_token') {
        console.log('Обнаружено изменение токена в localStorage');
        syncToken();
      }
    };

    // Слушаем изменения в том же окне (для случаев когда localStorage изменяется программно)
    let lastToken = localStorage.getItem('pb_token');
    const checkTokenChange = () => {
      const currentToken = localStorage.getItem('pb_token');
      if (currentToken !== lastToken) {
        console.log('Обнаружено изменение токена в текущем окне');
        lastToken = currentToken;
        syncToken();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Проверяем изменения каждые 2 секунды
    const interval = setInterval(checkTokenChange, 2000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  return null; // Компонент не рендерит ничего видимого
};
